sonar.projectKey=generix_legal-referential-front_AY8597ZTA1fmeBcZBcc5

#sonar.projectKey=legalReferentialFront
sonar.projectName=legalReferentialFront generated by jhipster

# Typescript tests files must be inside sources and tests, otherwise `INFO: Test execution data ignored for 80 unknown files, including:` 
# is shown.
sonar.sources = src
sonar.tests = src
#sonar.host.url=http://************:9000/

sonar.test.inclusions = src/test/**/*.*, src/main/webapp/app/**/*.spec.ts
#sonar.testExecutionReportPaths=target/test-results/jest/TESTS-results-sonar.xml
sonar.javascript.lcov.reportPaths = target/test-results/lcov.info

sonar.sourceEncoding = UTF-8
sonar.exclusions = src/main/webapp/content/**/*.*, src/main/webapp/i18n/*.js, target/classes/static/**/*.*

sonar.issue.ignore.multicriteria=
