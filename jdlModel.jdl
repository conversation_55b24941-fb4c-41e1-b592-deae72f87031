enum Statut {
    A,
    I,
    F,
    C
}

enum EstablishmentType {
    P,
    S
}

enum Nature {
    M,
    D
}

enum PlatformType {
    PPF,
    PDP,
    CPRO,
    NA
}

enum TypeEntiteDestinataire {
    PUBLIQUE,
    ASSUJETTI
}

enum Flux13RequestStatus {
    Pending,
    Rejected,
    Accepted
}

enum AuthoritiesConstants {
    LEGALREF_ADMIN,
    LEGALREF_BUSINESS,
    LEGALREF_CUSTOMER,
    LEGALREF_SYNCHRONIZER,
    ANONYMOUS
}

enum CheckStatus {
    GRANTED,
    FORBIDDEN,
    CRUD,
    RESTRICTED
}

entity Siret {
    instanceID Long,
    siret String,
    status Statut,
    establishmentType EstablishmentType,
    denomination String,
    addressLine1 String,
    addressLine2 String,
    addressLine3 String,
    addressCity String,
    addressCountrySubdivision String,
    addressCountryCode String,
    addressCountryLabel String,
    b2gMoa Boolean,
    b2gMoaOnly <PERSON>,
    b2gPaymentStatusManagement Boolean,
    b2gLegalEngagementManagement Boolean,
    b2gLegalOrServiceEngagementManagement Boolean,
    b2gServiceCodeManagement Boolean,
    diffusible Boolean,
    addressPostalCode String
}

entity AddressLine {
    instanceID Long,
    addressLineID String,
    addressSuffix String,
    effectBeginDate LocalDate,
    effectEndDate LocalDate,
    effectiveEndDate LocalDate,
    nature Nature,
    creationDate LocalDate,
    creationBy String,
    lastModificationDate Instant,
    modifiedBy String
}

entity Platform {
    instanceID Long,
    platformID String,
    platformType PlatformType,
    platformStatus Statut,
    platformMatricule String,
    platformSiren String,
    platformCompanyName String,
    platformCommercialName String,
    platformContactOrUrl String,
    registrationBeginDate LocalDate,
    registrationEndDate LocalDate
}

entity Siren {
    instanceID Long,
    siren String,
    companyName String,
    entityType TypeEntiteDestinataire,
    status Statut
}


entity RoutingCode {
    instanceID Long,
    routingCodeID String,
    status Statut,
    routingCodeLabel String,
    routingCodeType String,
    administrativeStatus String,
    legalEngagementManagement Boolean,
    addressLine1 String,
    addressLine2 String,
    addressLine3 String,
    addressPostalCode String,
    addressCity String,
    addressCountrySubdivision String,
    addressCountryCode String,
    addressCountryLabel String
}






// start

// defining multiple OneToMany relationships with comments
relationship OneToMany {
    Siren{pkSiret} to Siret {fkSiretSiren},
    Siret {fkAddressLineRoutingCode} to RoutingCode {fkRoutingCodeSiret},
}

relationship OneToMany {
    Siren {pkAddressLine} to AddressLine{fkAddressLineSiren},
    Siret {pkAddressLine} to AddressLine{fkAddressLineSiret}
}

relationship OneToOne {
    AddressLine{fkAddressLineRoutingCode} to RoutingCode{fkRoutingCodeAddressLine}
}


// Set pagination options
paginate Siren, Siret, RoutingCode, AddressLine with pagination

// Use Data Transfer Objects (DTO)
dto * with mapstruct

// Set service options to all except few
service all with serviceImpl

// Set an angular suffix
// angularSuffix * with mySuffix

filter Siren, Siret, RoutingCode, AddressLine, Platform



