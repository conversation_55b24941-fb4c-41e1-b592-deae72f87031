<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 https://maven.apache.org/xsd/assembly-2.2.0.xsd">
    <id>distribution</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>target/classes/static</directory>
            <outputDirectory>/app</outputDirectory>
            <includes>
                <include>**</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>setup/</directory>
            <outputDirectory>/setup</outputDirectory>

            <includes>
                <include>**</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
