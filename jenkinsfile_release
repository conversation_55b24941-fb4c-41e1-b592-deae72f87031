pipeline {
    agent any

       tools{
           nodejs '14.17.0'
       }
    stages {
        stage('Checkout') {
            steps {
                git credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a', url: 'http://git.chassagne-scm.generixgroup.com/generix/legal-referential-front.git'
                sh '''
                    git checkout ${BRANCH_NAME}
                '''
            }
        }

         stage('Get Version') {
         steps {
             script {
                 if (DEPLOY_TYPE == "snapshot") {
                     def version = sh(script: 'node -e "console.log(require(\'./package.json\').version)"', returnStdout: true).trim()
                     def parts = version.split('-')
                     def majorMinorMicro = parts[0]
                      actuel_version = version.contains('-SNAPSHOT') ? version : "${majorMinorMicro}-SNAPSHOT"
                     echo "The version from package.json is: ${actuel_version}"
                 }
             }
         }
     }



      stage('Extract Version') {
            steps {
                script {
                if(DEPLOY_TYPE == "qualif"){
                    version_without_suffix =sh(script: 'node -e "console.log(require(\'./package.json\').version)"', returnStdout: true).trim()
                    major = version_without_suffix.tokenize('.')[0]
                    minor = version_without_suffix.tokenize('.')[1]
                    micro = version_without_suffix.tokenize('.')[2]
                    new_minor = minor.toInteger() + 1
                    new_version = "${major}.${new_minor}.${micro}"
                    new_version= new_version.contains('-SNAPSHOT') ? new_version : "${new_version}-SNAPSHOT"
                    version_without_suffix=  version_without_suffix.split('-')[0]
                    echo "The old version is: ${version_without_suffix}"
                    echo "The new version is: ${new_version}"
                }
            }
            }
        }

        stage('Update Version Files') {
            steps {
                  script {
                if(DEPLOY_TYPE == "qualif"){
                nodejs(nodeJSInstallationName: 'NodeJS') {
                    sh "sed -i '0,/<version>/{s@<version>.*</version>@<version>${version_without_suffix}</version>@}' pom.xml"
                    sh "npm version ${version_without_suffix} --no-git-tag-version" }
                     // Git operations
                    sh 'git add pom.xml'
                    sh 'git add package.json'
                  sh "git commit -m 'Update version to ${version_without_suffix}'"
                }
            }
            }
        }

            stage('Tag and Push') {
            steps {
                script {
                    if (DEPLOY_TYPE == "qualif" || DEPLOY_TYPE == "snapshot") {
                          withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {

                            def tagVersion = DEPLOY_TYPE == "qualif" ? version_without_suffix : actuel_version
                            def statusCmd = sh(returnStdout: true, script: "git tag -l Legel-ref-${tagVersion}").trim()

                            if (statusCmd != "Legel-ref-${tagVersion}") {
                                echo "Tag Legel-ref-${tagVersion} not exist"
                            } else {
                                echo "Tag Legel-ref-${tagVersion} exist delete it"
                                sh """
                                git remote set-url origin http://$USERNAME:$<EMAIL>/generix/legal-referential-front
                                git push --delete origin Legel-ref-${tagVersion}
                                git tag --delete Legel-ref-${tagVersion}
                                """
                            }
                            if (DEPLOY_TYPE == "snapshot") {
                                    sh """
                                    git remote set-url origin http://$USERNAME:$<EMAIL>/generix/legal-referential-front
                                    git push origin ${BRANCH_NAME}
                                    git tag -a legel-ref-${tagVersion} -m 'Version ${tagVersion}'
                                    git push origin legel-ref-${tagVersion}
                                    """
                                } else if (DEPLOY_TYPE == "qualif"){
                                    def branchversion = version_without_suffix.tokenize('.')[0]+'.'+version_without_suffix.tokenize('.')[1]
                                    echo "QUALIF VERSION"

                                    sh """
                                    git remote set-url origin http://$USERNAME:$<EMAIL>/generix/legal-referential-front
                                    
                                    if git show-ref --verify --quiet qualif/${branchversion}; then
                                        echo "Branch qualif/${branchversion} already exists. Switching to it."
                                        git checkout qualif/${branchversion}
                                    else
                                        echo "Creating new branch qualif/${branchversion}."
                                        git checkout -b qualif/${branchversion}
                                        git push origin qualif/${branchversion}
                                    fi


                                    git checkout ${BRANCH_NAME}
                                    git push origin ${BRANCH_NAME}
                                    
                                    git tag -a legel-ref-${tagVersion} -m 'Version ${tagVersion}'
                                    git push origin legel-ref-${tagVersion}
                                    """
                                }

                        }
                    }


                  else if (DEPLOY_TYPE == "release") {
                           withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            sh """
                            git remote set-url origin http://$USERNAME:$<EMAIL>/generix/legal-referential-front
                            git tag -a qualif/${VERSION} Legel-ref-${VERSION} -m 'new release tag from qualif Legel-ref-${VERSION}'
                            git push origin qualif/${VERSION}
                            """
                        }
                    }
                }
            }
        }

        stage('Install Dependencies') {
            steps {
                 script {
                if(DEPLOY_TYPE != "release"){
                    sh 'npm version'
               sh 'npm install'

            }
                 }

            }
        }

        stage('Build , Package and push to nexus') {
             tools {

        maven 'Maven3.6.3'
         }
         steps {
              script {
                if(DEPLOY_TYPE != "release"){
                  withCredentials([usernamePassword(credentialsId: 'nexus-password', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                    sh 'mvn clean package'
                    sh 'rm -f ../settings.xml'
                    sh 'cp settings.xml ../settings.xml'
                    sh 'sed -i s/%USER%/$USERNAME/g ../settings.xml'
                    sh 'sed -i s/%PASSWORD%/$PASSWORD/g ../settings.xml'
                    sh 'mvn --settings ../settings.xml -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true deploy -P ${DEPLOY_TYPE}'
                  }
                 }
              }

         }
        }




        stage('Update Version Again') {
            steps {
                  script {
                if(DEPLOY_TYPE == "qualif"){
                   nodejs(nodeJSInstallationName: 'NodeJS') {
                    sh "sed -i '0,/<version>/{s@<version>.*</version>@<version>${new_version}</version>@}' pom.xml"
                    sh "npm version ${new_version} --no-git-tag-version" }
                // Git operations
                sh 'git add pom.xml'
                sh 'git add package.json'
                sh "git commit -m 'Update version to ${new_version}'"
                sh "git push origin ${BRANCH_NAME}"
            }
        }
        }
        }

            stage('Copy Qualif to Release') {
                 tools {

        maven 'Maven3.6.3'
         }
        steps {


script {
    if (DEPLOY_TYPE == "release") {
        def groupId = 'com.generix'  // Use periods (.) as separators
        def artifactId = 'legal-referential-front'
        def packaging = 'zip'
        def sourcePath = "${WORKSPACE}/${artifactId}-${VERSION}-distribution.zip"

        def targetRepositoryId = 'releasesLegalRef'
        def targetRepositoryUrl = '$NEXUS_URL/$PATH_REPO_NEXUS/$RELEASE_REPO_LEGAL_REF'

        def sourceUrl = "$NEXUS_URL/$PATH_REPO_NEXUS/$QUALIF_REPO_LEGEL_REF/${groupId.replace('.', '/')}/${artifactId}/${VERSION}/${artifactId}-${VERSION}-distribution.zip"
       withCredentials([usernamePassword(credentialsId: 'nexus-password', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
            sh """
            wget --no-check-certificate --http-user="${USERNAME}" --http-password="${PASSWORD}" -O "${sourcePath}" "${sourceUrl}"
                """
            sh 'rm -f ../settings.xml'
            sh 'cp settings.xml ../settings.xml'
            sh 'sed -i s/%USER%/$USERNAME/g ../settings.xml'
            sh 'sed -i s/%PASSWORD%/$PASSWORD/g ../settings.xml'

            }

        sh """
              mvn --settings ../settings.xml -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true deploy:deploy-file -DgroupId="${groupId}" -DartifactId="${artifactId}" -Dversion="${VERSION}" -Dpackaging="${packaging}" -Dfile="$sourcePath" -DrepositoryId="${targetRepositoryId}" -Durl="$targetRepositoryUrl"
              """
}
}
}

        }



    }

    post {
        always {
            deleteDir()
        cleanWs()


        }
    }
}
