{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"legal-referential-front": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src/main/webapp", "prefix": "jhi", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"allowedCommonJsDependencies": ["base64-js", "js-sha256"], "customWebpackConfig": {"path": "./webpack/webpack.custom.js"}, "outputPath": "target/classes/static/", "index": "src/main/webapp/index.html", "main": "src/main/webapp/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/main/webapp/content", "src/main/webapp/favicon.ico", "src/main/webapp/manifest.webapp", "src/main/webapp/robots.txt"], "styles": ["src/main/webapp/content/scss/vendor.scss", "src/main/webapp/content/scss/global.scss"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/main/webapp/environments/environment.ts", "with": "src/main/webapp/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "serviceWorker": true, "ngswConfigPath": "ngsw-config.json", "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}, "development": {"fileReplacements": [{"replace": "src/main/webapp/environments/environment.ts", "with": "src/main/webapp/environments/environment.dev.ts"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "options": {"buildTarget": "legal-referential-front:build:development", "port": 4200}, "configurations": {"production": {"buildTarget": "legal-referential-front:build:production"}, "development": {"buildTarget": "legal-referential-front:build:development"}}, "defaultConfiguration": "development"}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "jest.conf.js", "tsConfig": "tsconfig.spec.json"}}}}}, "cli": {"cache": {"enabled": true, "path": "./target/angular/", "environment": "all"}, "packageManager": "npm", "analytics": "************************************"}}