#!/bin/sh
echo 'Remove last config file'
rm -f /var/www/content/config/config.prod.json

rm -f -r /var/www/html

echo 'Add config File'

envsubst < /etc/apache2/config.template.json > /var/www/content/config/config.prod.json

a2enmod headers

rm -f /etc/apache2/apache2.conf

envsubst '$MY_APP_API_URL $MY_APP_API_URL_PROTOCOL $APPLICATION_CSP_URL_AUTH_SERVER' < /etc/apache2/apache2_bck.conf > /etc/apache2/apache2.conf




apache2ctl -D FOREGROUND

