# Stage 1: Compile and Build angular codebase

# Use official node image as the base image
FROM node:22.14.0 as build

# Set the working directory
WORKDIR /usr/local/app

COPY package*.json ./

# Add the source code to app
COPY .. /usr/local/app/

# Install all the dependencies
RUN npm install

# Generate the build of the application
RUN npm run build --prod


# Stage 2: Serve app with nginx server

# Use official nginx image as the base image
FROM ubuntu:14.04

RUN apt-get update
RUN apt-get install -y gettext
RUN apt-get install -y apache2
RUN apt-get install -y npm
RUN apt-get install -y apache2-utils
RUN apt-get install -y curl build-essential
RUN apt-get clean

# Enable the necessary Apache modules
RUN a2enmod rewrite

COPY ./setup/ports.conf /etc/apache2/
COPY ./setup/apache2.conf /etc/apache2/apache2_bck.conf
COPY ./setup/config.template.json /etc/apache2/
COPY ./setup/entrypoint.sh /etc/apache2/

RUN chmod 777 /etc/apache2/entrypoint.sh

# Copy the build output to replace the default nginx contents.
COPY --from=build /usr/local/app/target/classes/static /var/www/

ENTRYPOINT ["/etc/apache2/entrypoint.sh"]

# Expose port 80
EXPOSE 4200/tcp
