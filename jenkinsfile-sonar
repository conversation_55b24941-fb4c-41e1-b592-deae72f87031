pipeline {
    agent any
    environment{
        imageName = "chassagne/aio-front"
        dockerImage = ""
    }
    tools {
        nodejs '14.17.0'
        maven 'Maven3.6.3'
    }

    stages {
        stage('Git Checkout') {
            steps {
                checkout scm       
            }
        }
        stage('SonarQube Scan') {
            environment {
                //Define the path to the SonarScanner tool
                SCANNER_HOME = tool 'SonarScanner'
                //SCANNER_HOME = '/opt/sonar-scanner'
            }
            steps {
                script {
                    //Run SonarQube Scanner with the project key
                    withSonarQubeEnv('Sonarqube') {
                        sh 'npm install'
                        sh 'npm run build --prod'
                        sh "${env.SCANNER_HOME}/bin/sonar-scanner"
                    }
                }
            }
        }
        stage("Quality gate") {
            steps {
                timeout(time: 2, unit: 'MINUTES') {
                    script {
                        def qg = waitForQualityGate()
                        if (qg == null) {
                            error "Pipeline aborted due to timeout waiting for quality gate"
                        }
                        if (qg.status != 'OK') {
                            error "Pipeline aborted due to quality gate failure: ${qg.status}"
                        }
                    }
                }
            }
        }
    }
    post {
        always {
            cleanWs() 
        }
    }

}

