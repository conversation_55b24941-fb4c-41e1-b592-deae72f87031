<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <groupId>com.generix</groupId>
  <artifactId>legal-referential-front</artifactId>
  <name>[MVN] Legal-Referential-front Project - POM</name>
  <description>Legal-Referential Project.</description>
  <version>3.0.0-SNAPSHOT</version>


  <properties>
    <ddl-imports/>
    <maven.build.timestamp.format>yyyy-MM-dd HH:mmZ</maven.build.timestamp.format>
    <template.generixcustomer>generixcustomer</template.generixcustomer>
    <template.generix>generix</template.generix>
  </properties>

  <profiles>
    <profile>
      <id>release</id>
      <distributionManagement>
        <repository>
            <id>releasesLegalRef</id>
            <url>https://nexus.chassagne-repo.generixgroup.com/content/repositories/releasesLegal-Ref</url>
        </repository>
        <snapshotRepository>
            <id>snapshotsForLegalref</id>
            <url>https://nexus.chassagne-repo.generixgroup.com/content/repositories/Legal-ref-Snapshots</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
    <profile>
      <id>qualif</id>
      <distributionManagement>
        <repository>
            <id>legalRefToQualify</id>
            <url>https://nexus.chassagne-repo.generixgroup.com/content/repositories/legalrefToQualif</url>
        </repository>
        <snapshotRepository>
            <id>snapshotsForLegalref</id>
            <url>https://nexus.chassagne-repo.generixgroup.com/content/repositories/Legal-ref-Snapshots</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
  </profiles>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>none</phase>
            <configuration>
              <finalName>unwanted</finalName>
              <classifier>unwanted</classifier>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>1.3.2</version>
        <executions>
          <execution>
            <id>npm install</id>
            <goals>
              <goal>exec</goal>
            </goals>
            <phase>initialize</phase>
            <configuration>
              <executable>npm</executable>
              <arguments>
                <argument>install</argument>
              </arguments>
            </configuration>
          </execution>
          <execution>
            <id>npm build</id>
            <goals>
              <goal>exec</goal>
            </goals>
            <phase>package</phase>
            <configuration>
              <executable>npm</executable>
              <arguments>
                <argument>run</argument>
                <argument>build</argument>
                <argument>-- --configuration=production</argument>
              </arguments>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <executions>
          <execution>
            <id>create-zip</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <descriptors>
                <descriptor>assembly.xml</descriptor>
              </descriptors>
              <finalName>${project.groupId}-${project.artifactId}-${project.version}</finalName>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.8</version>
        <executions>
          <execution>
            <id>default-deploy</id>
            <phase>deploy</phase>
            <goals>
              <goal>deploy</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
