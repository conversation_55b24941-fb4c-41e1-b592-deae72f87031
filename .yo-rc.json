{"generator-jhipster": {"baseName": "legalReferentialFront", "clientFramework": "angular", "clientTestFrameworks": [], "clientTheme": "none", "creationTimestamp": *************, "devServerPort": 4200, "enableTranslation": true, "entities": ["<PERSON><PERSON>", "<PERSON><PERSON>", "RoutingCode", "AddressLine", "Platform"], "jhipsterVersion": "8.7.3", "languages": ["fr", "en"], "lastLiquibaseTimestamp": *************, "microfrontend": null, "microfrontends": [], "nativeLanguage": "fr", "skipServer": true, "testFrameworks": [], "withAdminUi": true}}