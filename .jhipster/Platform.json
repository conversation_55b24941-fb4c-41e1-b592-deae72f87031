{"annotations": {"changelogDate": "20250418105459"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "platformID", "fieldType": "String"}, {"fieldName": "platformType", "fieldType": "PlatformType", "fieldValues": "PPF,PDP,CPRO,NA"}, {"fieldName": "platformStatus", "fieldType": "Statut", "fieldValues": "A,I,F,C"}, {"fieldName": "platformMatricule", "fieldType": "String"}, {"fieldName": "platformSiren", "fieldType": "String"}, {"fieldName": "platformCompanyName", "fieldType": "String"}, {"fieldName": "platformCommercialName", "fieldType": "String"}, {"fieldName": "platformContactOrUrl", "fieldType": "String"}, {"fieldName": "registrationBeginDate", "fieldType": "LocalDate"}, {"fieldName": "registrationEndDate", "fieldType": "LocalDate"}], "jpaMetamodelFiltering": true, "name": "Platform", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "siren", "otherEntityRelationshipName": "fkSirenPlatform", "relationshipName": "pkSiren", "relationshipSide": "left", "relationshipType": "one-to-many"}], "service": "serviceImpl"}