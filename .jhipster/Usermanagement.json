{"annotations": {"changelogDate": "20250418141547"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "username", "fieldType": "String"}, {"fieldName": "firstName", "fieldType": "String"}, {"fieldName": "lastName", "fieldType": "String"}, {"fieldName": "email", "fieldType": "String"}, {"fieldName": "enabled", "fieldType": "Boolean"}, {"fieldName": "roles", "fieldType": "List"}, {"fieldName": "password", "fieldType": "String"}], "jpaMetamodelFiltering": true, "name": "Usermanagement", "pagination": "pagination", "relationships": [], "service": "serviceImpl"}