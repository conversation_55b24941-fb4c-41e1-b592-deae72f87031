{"annotations": {"changelogDate": "20230928124818"}, "applications": "*", "changelogDate": "20230928124818", "dto": "mapstruct", "fields": [{"fieldName": "instanceID", "fieldType": "<PERSON>"}, {"fieldName": "addressLineID", "fieldType": "String"}, {"fieldName": "addressSuffix", "fieldType": "String"}, {"fieldName": "effectBeginDate", "fieldType": "LocalDate"}, {"fieldName": "effectEndDate", "fieldType": "LocalDate"}, {"fieldName": "effectiveEndDate", "fieldType": "LocalDate"}, {"fieldName": "nature", "fieldType": "Nature", "fieldValues": "<PERSON>,<PERSON>"}, {"fieldName": "creationDate", "fieldType": "LocalDate"}, {"fieldName": "creationBy", "fieldType": "String"}, {"fieldName": "lastModificationDate", "fieldType": "Instant"}, {"fieldName": "modifiedBy", "fieldType": "String"}], "jpaMetamodelFiltering": true, "name": "AddressLine", "pagination": "pagination", "relationships": [{"otherEntityName": "routingCode", "otherEntityRelationshipName": "fkRoutingCodeAddressLine", "relationshipName": "pkRoutingCode", "relationshipSide": "left", "relationshipType": "one-to-one"}, {"otherEntityName": "siren", "otherEntityRelationshipName": "pkAddressLine", "relationshipName": "fkAddressLineSiren", "relationshipSide": "right", "relationshipType": "many-to-one"}, {"otherEntityName": "siret", "otherEntityRelationshipName": "pkAddressLine", "relationshipName": "fkAddressLineSiret", "relationshipSide": "right", "relationshipType": "many-to-one"}], "service": "serviceImpl"}