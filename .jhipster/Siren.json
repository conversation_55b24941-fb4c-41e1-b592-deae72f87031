{"annotations": {"changelogDate": "20230928124815"}, "applications": "*", "changelogDate": "20230928124815", "dto": "mapstruct", "fields": [{"fieldName": "instanceID", "fieldType": "<PERSON>"}, {"fieldName": "siren", "fieldType": "String"}, {"fieldName": "companyName", "fieldType": "String"}, {"fieldName": "entityType", "fieldType": "TypeEntiteDestinataire", "fieldValues": "PUBLIQUE,ASSUJETTI"}, {"fieldName": "status", "fieldType": "Statut", "fieldValues": "A,I,F,C"}], "jpaMetamodelFiltering": true, "name": "<PERSON><PERSON>", "pagination": "pagination", "relationships": [{"otherEntityName": "siret", "otherEntityRelationshipName": "fkSiretSiren", "relationshipName": "pkSiret", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "addressLine", "otherEntityRelationshipName": "fkAddressLineSiren", "relationshipName": "pkAddressLine", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "platform", "otherEntityRelationshipName": "pkSiren", "relationshipName": "fkSirenPlatform", "relationshipSide": "right", "relationshipType": "many-to-one"}], "service": "serviceImpl"}