{"annotations": {"changelogDate": "**************"}, "applications": "*", "changelogDate": "**************", "dto": "mapstruct", "fields": [{"fieldName": "instanceID", "fieldType": "<PERSON>"}, {"fieldName": "routingCodeID", "fieldType": "String"}, {"fieldName": "status", "fieldType": "Statut", "fieldValues": "A,I,F,C"}, {"fieldName": "routingCodeLabel", "fieldType": "String"}, {"fieldName": "routingCodeType", "fieldType": "String"}, {"fieldName": "administrativeStatus", "fieldType": "String"}, {"fieldName": "legalEngagementManagement", "fieldType": "Boolean"}, {"fieldName": "addressLine1", "fieldType": "String"}, {"fieldName": "addressLine2", "fieldType": "String"}, {"fieldName": "addressLine3", "fieldType": "String"}, {"fieldName": "addressPostalCode", "fieldType": "String"}, {"fieldName": "addressCity", "fieldType": "String"}, {"fieldName": "addressCountrySubdivision", "fieldType": "String"}, {"fieldName": "addressCountryCode", "fieldType": "String"}, {"fieldName": "addressCountryLabel", "fieldType": "String"}], "jpaMetamodelFiltering": true, "name": "RoutingCode", "pagination": "pagination", "relationships": [{"otherEntityName": "addressLine", "otherEntityRelationshipName": "pkRoutingCode", "relationshipName": "fkRoutingCodeAddressLine", "relationshipSide": "right", "relationshipType": "one-to-one"}, {"otherEntityName": "siret", "otherEntityRelationshipName": "pkRoutingCode", "relationshipName": "fkRoutingCodeSiret", "relationshipSide": "right", "relationshipType": "many-to-one"}], "service": "serviceImpl"}