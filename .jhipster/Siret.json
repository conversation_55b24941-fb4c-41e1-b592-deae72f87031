{"annotations": {"changelogDate": "20230928124816"}, "applications": "*", "changelogDate": "20230928124816", "dto": "mapstruct", "fields": [{"fieldName": "instanceID", "fieldType": "<PERSON>"}, {"fieldName": "siret", "fieldType": "String"}, {"fieldName": "status", "fieldType": "Statut", "fieldValues": "A,I,F,C"}, {"fieldName": "establishmentType", "fieldType": "EstablishmentType", "fieldValues": "<PERSON>,<PERSON>"}, {"fieldName": "denomination", "fieldType": "String"}, {"fieldName": "addressLine1", "fieldType": "String"}, {"fieldName": "addressLine2", "fieldType": "String"}, {"fieldName": "addressLine3", "fieldType": "String"}, {"fieldName": "addressCity", "fieldType": "String"}, {"fieldName": "addressCountrySubdivision", "fieldType": "String"}, {"fieldName": "addressCountryCode", "fieldType": "String"}, {"fieldName": "addressCountryLabel", "fieldType": "String"}, {"fieldName": "b2gMoa", "fieldType": "Boolean"}, {"fieldName": "b2gMoaOnly", "fieldType": "Boolean"}, {"fieldName": "b2gPaymentStatusManagement", "fieldType": "Boolean"}, {"fieldName": "b2gLegalEngagementManagement", "fieldType": "Boolean"}, {"fieldName": "b2gLegalOrServiceEngagementManagement", "fieldType": "Boolean"}, {"fieldName": "b2gServiceCodeManagement", "fieldType": "Boolean"}, {"fieldName": "diffusible", "fieldType": "Boolean"}, {"fieldName": "addressPostalCode", "fieldType": "String"}], "jpaMetamodelFiltering": true, "name": "<PERSON><PERSON>", "pagination": "pagination", "relationships": [{"otherEntityName": "routingCode", "otherEntityRelationshipName": "fkRoutingCodeSiret", "relationshipName": "pkRoutingCode", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "addressLine", "otherEntityRelationshipName": "fkAddressLineSiret", "relationshipName": "pkAddressLine", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "siren", "otherEntityRelationshipName": "pkSiret", "relationshipName": "fkSiretSiren", "relationshipSide": "right", "relationshipType": "many-to-one"}], "service": "serviceImpl"}