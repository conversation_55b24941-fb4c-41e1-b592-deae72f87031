pipeline {
    agent any
    tools {
        nodejs '14.17.0'
        maven 'Maven3.6.3'
    }
    stages {
        stage('Checkout') {
            steps {
                git credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a', url: 'http://git.chassagne-scm.generixgroup.com/generix/legal-referential-front.git'
                sh '''
                git checkout ${BRANCH_NAME}
                '''
            }
        }
        stage('SonarQube Scan') {
            environment {
                // Define the path to the SonarScanner tool
                SCANNER_HOME = tool 'SonarScanner'
                //SCANNER_HOME = '/opt/sonar-scanner'
            }
            steps {
                script {
                    // Run SonarQube Scanner with the project key
                    withSonarQubeEnv('Sonarqube') {
                        sh 'npm install'
                        sh 'npm run build --prod'
                        sh "${env.SCANNER_HOME}/bin/sonar-scanner"
                    }
                }
            }
        }

        stage('Build and Push Docker Image - Legal Referential Front') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'Docker-Hub-credentials', usernameVariable: 'DOCKER_HUB_USERNAME', passwordVariable: 'DOCKER_HUB_TOKEN')]) {
                        sh """
                        docker login -u ${DOCKER_HUB_USERNAME} -p ${DOCKER_HUB_TOKEN}
                        docker build -t chassagne/legal-referential-front:${VERSION_TAG} .
                        docker push chassagne/legal-referential-front:${VERSION_TAG}
                        """
                    }
                }
            }
        }
    }
     post {
        always {
            deleteDir()
              cleanWs()
          //sh "docker rmi chassagne/legal-referential-front:${VERSION_TAG} -f "

        }
    }
}
