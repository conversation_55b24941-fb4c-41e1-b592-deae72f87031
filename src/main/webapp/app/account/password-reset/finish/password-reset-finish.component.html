<div>
  <div class="d-flex justify-content-center">
    <div class="col-md-4">
      <h1 jhiTranslate="reset.finish.title">Réinitialisation du mot de passe</h1>

      @if (initialized() && !key()) {
        <div class="alert alert-danger" jhiTranslate="reset.finish.messages.keymissing">La clef de réinitilisation est manquante</div>
      }

      @if (key() && !success()) {
        <div class="alert alert-warning">
          <span jhiTranslate="reset.finish.messages.info">Choisir un nouveau mot de passe</span>
        </div>
      }

      @if (error()) {
        <div class="alert alert-danger">
          <span jhiTranslate="reset.finish.messages.error"
            >Votre mot de passe n&apos;a pas pu être réinitialisé. La demande de réinitialisation n&apos;est valable que 24 heures.</span
          >
        </div>
      }

      @if (success()) {
        <div class="alert alert-success">
          <span jhiTranslate="reset.finish.messages.success"><strong>Votre mot de passe a été réinitialisé.</strong> Merci de </span>
          <a class="alert-link" routerLink="/login" jhiTranslate="global.messages.info.authenticated.link">connecter</a>.
        </div>
      }

      @if (doNotMatch()) {
        <div class="alert alert-danger" jhiTranslate="global.messages.error.dontmatch">
          Le nouveau mot de passe et sa confirmation ne sont pas égaux !
        </div>
      }

      @if (key() && !success()) {
        <div>
          <form name="form" (ngSubmit)="finishReset()" [formGroup]="passwordForm">
            <div class="mb-3">
              <label class="form-label" for="newPassword" jhiTranslate="global.form.newpassword.label">Nouveau mot de passe</label>
              <input
                type="password"
                class="form-control"
                id="newPassword"
                name="newPassword"
                placeholder="{{ 'global.form.newpassword.placeholder' | translate }}"
                formControlName="newPassword"
                data-cy="resetPassword"
                #newPassword
              />

              @let newPasswordRef = passwordForm.get('newPassword')!;
              @if (newPasswordRef.invalid && (newPasswordRef.dirty || newPasswordRef.touched)) {
                <div>
                  @if (newPasswordRef?.errors?.required) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.required"
                      >Votre mot de passe est requis.</small
                    >
                  }

                  @if (newPasswordRef?.errors?.minlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.minlength"
                      >Votre mot de passe doit comporter au moins 4 caractères.</small
                    >
                  }

                  @if (newPasswordRef?.errors?.maxlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.maxlength"
                      >Votre mot de passe ne doit pas comporter plus de 50 caractères.</small
                    >
                  }
                </div>
              }

              <jhi-password-strength-bar [passwordToCheck]="newPasswordRef.value"></jhi-password-strength-bar>
            </div>

            <div class="mb-3">
              <label class="form-label" for="confirmPassword" jhiTranslate="global.form.confirmpassword.label"
                >Confirmation du nouveau mot de passe</label
              >
              <input
                type="password"
                class="form-control"
                id="confirmPassword"
                name="confirmPassword"
                placeholder="{{ 'global.form.confirmpassword.placeholder' | translate }}"
                formControlName="confirmPassword"
                data-cy="confirmResetPassword"
              />

              @let confirmPasswordRef = passwordForm.get('confirmPassword')!;
              @if (confirmPasswordRef.invalid && (confirmPasswordRef.dirty || confirmPasswordRef.touched)) {
                <div>
                  @if (confirmPasswordRef?.errors?.required) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.required"
                      >Votre confirmation du mot de passe est requise.</small
                    >
                  }

                  @if (confirmPasswordRef?.errors?.minlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.minlength"
                      >Votre confirmation du mot de passe doit comporter au moins 4 caractères.</small
                    >
                  }

                  @if (confirmPasswordRef?.errors?.maxlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.maxlength"
                      >Votre confirmation du mot de passe ne doit pas comporter plus de 50 caractères.</small
                    >
                  }
                </div>
              }
            </div>

            <button
              type="submit"
              [disabled]="passwordForm.invalid"
              class="btn btn-primary"
              data-cy="submit"
              jhiTranslate="reset.finish.form.button"
            >
              Réinitialiser le mot de passe
            </button>
          </form>
        </div>
      }
    </div>
  </div>
</div>
