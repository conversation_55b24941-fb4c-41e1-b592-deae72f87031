<div>
  <div class="d-flex justify-content-center">
    <div class="col-md-8">
      <h1 jhiTranslate="reset.request.title">Réinitialiser son mot de passe</h1>

      <jhi-alert-error></jhi-alert-error>

      @if (!success()) {
        <div class="alert alert-warning">
          <span jhiTranslate="reset.request.messages.info">Veuillez renseigner l&apos;adresse email utilisée pour vous enregistrer</span>
        </div>
        <form name="form" (ngSubmit)="requestReset()" [formGroup]="resetRequestForm">
          <div class="mb-3">
            <label class="form-label" for="email" jhiTranslate="global.form.email.label">Email</label>
            <input
              type="email"
              class="form-control"
              id="email"
              name="email"
              placeholder="{{ 'global.form.email.placeholder' | translate }}"
              formControlName="email"
              data-cy="emailResetPassword"
              #email
            />

            @let emailRef = resetRequestForm.get('email')!;
            @if (emailRef.invalid && (emailRef.dirty || emailRef.touched)) {
              <div>
                @if (emailRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.required"
                    >Votre email est requis.</small
                  >
                }
                @if (emailRef?.errors?.email) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.invalid"
                    >Votre email n&apos;est pas valide.</small
                  >
                }

                @if (emailRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.minlength"
                    >Votre email doit comporter au moins 5 caractères.</small
                  >
                }

                @if (emailRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.maxlength"
                    >Votre email ne doit pas comporter plus de 50 caractères.</small
                  >
                }
              </div>
            }
          </div>
          <button
            type="submit"
            [disabled]="resetRequestForm.invalid"
            class="btn btn-primary"
            data-cy="submit"
            jhiTranslate="reset.request.form.button"
          >
            Réinitialiser le mot de passe
          </button>
        </form>
      } @else {
        <div class="alert alert-success">
          <span jhiTranslate="reset.request.messages.success"
            >Veuillez vérifier vos nouveaux emails et suivre les instructions pour réinitialiser votre mot de passe.</span
          >
        </div>
      }
    </div>
  </div>
</div>
