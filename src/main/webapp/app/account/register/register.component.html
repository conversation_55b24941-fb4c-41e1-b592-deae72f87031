<div>
  <div class="d-flex justify-content-center">
    <div class="col-md-8">
      <h1 data-cy="registerTitle" jhiTranslate="register.title">Création de compte utilisateur</h1>

      @if (success()) {
        <div class="alert alert-success" jhiTranslate="register.messages.success">
          <strong>Compte enregistré !</strong> Merci de vérifier votre email de confirmation.
        </div>
      }

      @if (error()) {
        <div class="alert alert-danger" jhiTranslate="register.messages.error.fail">
          <strong>Compte non créé !</strong> Merci d&apos;essayer à nouveau plus tard.
        </div>
      }

      @if (errorUserExists()) {
        <div class="alert alert-danger" jhiTranslate="register.messages.error.userexists">
          <strong>Ce compte utilisateur existe déjà !</strong> Veuillez en choisir un autre.
        </div>
      }

      @if (errorEmailExists()) {
        <div class="alert alert-danger" jhiTranslate="register.messages.error.emailexists">
          <strong>Cet email est déjà utilisé !</strong> Veuillez en choisir un autre.
        </div>
      }

      @if (doNotMatch()) {
        <div class="alert alert-danger" jhiTranslate="global.messages.error.dontmatch">
          Le nouveau mot de passe et sa confirmation ne sont pas égaux !
        </div>
      }
    </div>
  </div>

  <div class="d-flex justify-content-center">
    <div class="col-md-8">
      @if (!success()) {
        <form name="form" (ngSubmit)="register()" [formGroup]="registerForm">
          <div class="mb-3">
            <label class="form-label" for="login" jhiTranslate="global.form.username.label">Nom d&apos;utilisateur</label>
            <input
              type="text"
              class="form-control"
              id="login"
              name="login"
              placeholder="{{ 'global.form.username.placeholder' | translate }}"
              formControlName="login"
              data-cy="username"
              #login
            />

            @let loginRef = registerForm.get('login')!;
            @if (loginRef.invalid && (loginRef.dirty || loginRef.touched)) {
              <div>
                @if (loginRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="register.messages.validate.login.required"
                    >Votre nom d&apos;utilisateur est obligatoire.</small
                  >
                }

                @if (loginRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="register.messages.validate.login.minlength"
                    >Votre nom d&apos;utilisateur doit contenir plus d&apos;un caractère.</small
                  >
                }

                @if (loginRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="register.messages.validate.login.maxlength"
                    >Votre nom d&apos;utilisateur ne peut pas contenir plus de 50 caractères.</small
                  >
                }

                @if (loginRef?.errors?.pattern) {
                  <small class="form-text text-danger" jhiTranslate="register.messages.validate.login.pattern"
                    >Votre nom d&apos;utilisateur est invalide.</small
                  >
                }
              </div>
            }
          </div>

          <div class="mb-3">
            <label class="form-label" for="email" jhiTranslate="global.form.email.label">Email</label>
            <input
              type="email"
              class="form-control"
              id="email"
              name="email"
              placeholder="{{ 'global.form.email.placeholder' | translate }}"
              formControlName="email"
              data-cy="email"
            />

            @let emailRef = registerForm.get('email')!;
            @if (emailRef.invalid && (emailRef.dirty || emailRef.touched)) {
              <div>
                @if (emailRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.required"
                    >Votre email est requis.</small
                  >
                }

                @if (emailRef?.errors?.invalid) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.invalid"
                    >Votre email n&apos;est pas valide.</small
                  >
                }

                @if (emailRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.minlength"
                    >Votre email doit comporter au moins 5 caractères.</small
                  >
                }

                @if (emailRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.maxlength"
                    >Votre email ne doit pas comporter plus de 50 caractères.</small
                  >
                }
              </div>
            }
          </div>

          <div class="mb-3">
            <label class="form-label" for="password" jhiTranslate="global.form.newpassword.label">Nouveau mot de passe</label>
            <input
              type="password"
              class="form-control"
              id="password"
              name="password"
              placeholder="{{ 'global.form.newpassword.placeholder' | translate }}"
              formControlName="password"
              data-cy="firstPassword"
            />

            @let passwordRef = registerForm.get('password')!;
            @if (passwordRef.invalid && (passwordRef.dirty || passwordRef.touched)) {
              <div>
                @if (passwordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.required"
                    >Votre mot de passe est requis.</small
                  >
                }

                @if (passwordRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.minlength"
                    >Votre mot de passe doit comporter au moins 4 caractères.</small
                  >
                }

                @if (passwordRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.maxlength"
                    >Votre mot de passe ne doit pas comporter plus de 50 caractères.</small
                  >
                }
              </div>
            }

            <jhi-password-strength-bar [passwordToCheck]="passwordRef.value"></jhi-password-strength-bar>
          </div>

          <div class="mb-3">
            <label class="form-label" for="confirmPassword" jhiTranslate="global.form.confirmpassword.label"
              >Confirmation du nouveau mot de passe</label
            >
            <input
              type="password"
              class="form-control"
              id="confirmPassword"
              name="confirmPassword"
              placeholder="{{ 'global.form.confirmpassword.placeholder' | translate }}"
              formControlName="confirmPassword"
              data-cy="secondPassword"
            />

            @let confirmPasswordRef = registerForm.get('confirmPassword')!;
            @if (confirmPasswordRef.invalid && (confirmPasswordRef.dirty || confirmPasswordRef.touched)) {
              <div>
                @if (confirmPasswordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.required"
                    >Votre confirmation du mot de passe est requise.</small
                  >
                }

                @if (confirmPasswordRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.minlength"
                    >Votre confirmation du mot de passe doit comporter au moins 4 caractères.</small
                  >
                }

                @if (confirmPasswordRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.maxlength"
                    >Votre confirmation du mot de passe ne doit pas comporter plus de 50 caractères.</small
                  >
                }
              </div>
            }
          </div>

          <button
            type="submit"
            [disabled]="registerForm.invalid"
            class="btn btn-primary"
            data-cy="submit"
            jhiTranslate="register.form.button"
          >
            Enregistrement
          </button>
        </form>
      }

      <div class="mt-3 alert alert-warning">
        <a class="alert-link" routerLink="/login" jhiTranslate="global.messages.info.authenticated.link">connecter</a>
      </div>
    </div>
  </div>
</div>
