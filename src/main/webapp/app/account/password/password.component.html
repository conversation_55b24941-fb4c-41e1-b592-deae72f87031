<div>
  <div class="d-flex justify-content-center">
    @if (account$ | async; as account) {
      <div class="col-md-8">
        <h2 jhiTranslate="password.title" [translateValues]="{ username: account.login }">
          Changer le mot de passe pour [<strong>{{ account.login }}</strong
          >]
        </h2>

        @if (success()) {
          <div class="alert alert-success" jhiTranslate="password.messages.success"><strong>Le mot de passe a été modifié !</strong></div>
        }
        @if (error()) {
          <div class="alert alert-danger" jhiTranslate="password.messages.error">
            <strong>Une erreur est survenue !</strong> Le mot de passe n&apos;a pas pu être modifié.
          </div>
        }
        @if (doNotMatch()) {
          <div class="alert alert-danger" jhiTranslate="global.messages.error.dontmatch">
            Le nouveau mot de passe et sa confirmation ne sont pas égaux !
          </div>
        }

        <form name="form" (ngSubmit)="changePassword()" [formGroup]="passwordForm">
          <div class="mb-3">
            <label class="form-label" for="currentPassword" jhiTranslate="global.form.currentpassword.label">Mot de passe actuel</label>
            <input
              type="password"
              class="form-control"
              id="currentPassword"
              name="currentPassword"
              placeholder="{{ 'global.form.currentpassword.placeholder' | translate }}"
              formControlName="currentPassword"
              data-cy="currentPassword"
            />

            @let currentPasswordRef = passwordForm.get('currentPassword')!;
            @if (currentPasswordRef.invalid && (currentPasswordRef.dirty || currentPasswordRef.touched)) {
              <div>
                @if (currentPasswordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.required"
                    >Votre mot de passe est requis.</small
                  >
                }
              </div>
            }
          </div>

          <div class="mb-3">
            <label class="form-label" for="newPassword" jhiTranslate="global.form.newpassword.label">Nouveau mot de passe</label>
            <input
              type="password"
              class="form-control"
              id="newPassword"
              name="newPassword"
              placeholder="{{ 'global.form.newpassword.placeholder' | translate }}"
              formControlName="newPassword"
              data-cy="newPassword"
            />

            @let newPasswordRef = passwordForm.get('newPassword')!;
            @if (newPasswordRef.invalid && (newPasswordRef.dirty || newPasswordRef.touched)) {
              <div>
                @if (newPasswordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.required"
                    >Votre mot de passe est requis.</small
                  >
                }

                @if (newPasswordRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.minlength"
                    >Votre mot de passe doit comporter au moins 4 caractères.</small
                  >
                }

                @if (newPasswordRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.maxlength"
                    >Votre mot de passe ne doit pas comporter plus de 50 caractères.</small
                  >
                }
              </div>
            }

            <jhi-password-strength-bar [passwordToCheck]="newPasswordRef.value"></jhi-password-strength-bar>
          </div>

          <div class="mb-3">
            <label class="form-label" for="confirmPassword" jhiTranslate="global.form.confirmpassword.label"
              >Confirmation du nouveau mot de passe</label
            >
            <input
              type="password"
              class="form-control"
              id="confirmPassword"
              name="confirmPassword"
              placeholder="{{ 'global.form.confirmpassword.placeholder' | translate }}"
              formControlName="confirmPassword"
              data-cy="confirmPassword"
            />

            @let confirmPasswordRef = passwordForm.get('confirmPassword')!;
            @if (confirmPasswordRef.invalid && (confirmPasswordRef.dirty || confirmPasswordRef.touched)) {
              <div>
                @if (confirmPasswordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.required"
                    >Votre confirmation du mot de passe est requise.</small
                  >
                }

                @if (confirmPasswordRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.minlength"
                    >Votre confirmation du mot de passe doit comporter au moins 4 caractères.</small
                  >
                }

                @if (confirmPasswordRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.maxlength"
                    >Votre confirmation du mot de passe ne doit pas comporter plus de 50 caractères.</small
                  >
                }
              </div>
            }
          </div>

          <button
            type="submit"
            [disabled]="passwordForm.invalid"
            class="btn btn-primary"
            data-cy="submit"
            jhiTranslate="password.form.button"
          >
            Sauvegarder
          </button>
        </form>
      </div>
    }
  </div>
</div>
