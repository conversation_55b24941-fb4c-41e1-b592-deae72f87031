import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { IUserManagement, NewUserManagement } from '../user-management.model';

export type PartialUpdateUserManagement = Partial<IUserManagement> & Pick<IUserManagement, 'id'>;

export type EntityResponseType = HttpResponse<IUserManagement>;
export type EntityArrayResponseType = HttpResponse<IUserManagement[]>;

@Injectable({ providedIn: 'root' })
export class UserManagementService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/authserv/admin/users');

  create(usermanagement: NewUserManagement): Observable<EntityResponseType> {
    return this.http.post<IUserManagement>(this.resourceUrl, usermanagement, { observe: 'response' });
  }

  update(usermanagement: IUserManagement): Observable<EntityResponseType> {
    return this.http.put<IUserManagement>(`${this.resourceUrl}/${this.getUserManagementIdentifier(usermanagement)}`, usermanagement, {
      observe: 'response',
    });
  }

  partialUpdate(usermanagement: PartialUpdateUserManagement): Observable<EntityResponseType> {
    return this.http.patch<IUserManagement>(`${this.resourceUrl}/${this.getUserManagementIdentifier(usermanagement)}`, usermanagement, {
      observe: 'response',
    });
  }

  find(id: number): Observable<EntityResponseType> {
    return this.http.get<IUserManagement>(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<IUserManagement[]>(this.resourceUrl, { params: options, observe: 'response' });
  }

  delete(id: number): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  getUserManagementIdentifier(usermanagement: Pick<IUserManagement, 'id'>): number {
    return usermanagement.id;
  }

  compareUserManagement(o1: Pick<IUserManagement, 'id'> | null, o2: Pick<IUserManagement, 'id'> | null): boolean {
    return o1 && o2 ? this.getUserManagementIdentifier(o1) === this.getUserManagementIdentifier(o2) : o1 === o2;
  }

  addUserManagementToCollectionIfMissing<Type extends Pick<IUserManagement, 'id'>>(
    usermanagementCollection: Type[],
    ...usermanagementsToCheck: (Type | null | undefined)[]
  ): Type[] {
    const usermanagements: Type[] = usermanagementsToCheck.filter(isPresent);
    if (usermanagements.length > 0) {
      const usermanagementCollectionIdentifiers = usermanagementCollection.map(usermanagementItem =>
        this.getUserManagementIdentifier(usermanagementItem),
      );
      const usermanagementsToAdd = usermanagements.filter(usermanagementItem => {
        const usermanagementIdentifier = this.getUserManagementIdentifier(usermanagementItem);
        if (usermanagementCollectionIdentifiers.includes(usermanagementIdentifier)) {
          return false;
        }
        usermanagementCollectionIdentifiers.push(usermanagementIdentifier);
        return true;
      });
      return [...usermanagementsToAdd, ...usermanagementCollection];
    }
    return usermanagementCollection;
  }

  authorities(): Observable<any[]> {
    return this.http.get<string[]>(this.applicationConfigService.getEndpointFor('api/authserv/admin/authorities'));
  }
}
