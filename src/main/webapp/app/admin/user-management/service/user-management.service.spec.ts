import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';

import { IUserManagement } from '../user-management.model';

import { UserManagementService } from './user-management.service';
import { sampleWithFullData, sampleWithNewData, sampleWithPartialData, sampleWithRequiredData } from '../user-management.test-samples';

const requireRestSample: IUserManagement = {
  ...sampleWithRequiredData,
};

describe('UserManagement Service', () => {
  let service: UserManagementService;
  let httpMock: HttpTestingController;
  let expectedResult: IUserManagement | IUserManagement[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    expectedResult = null;
    service = TestBed.inject(UserManagementService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find(123).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a UserManagement', () => {
      const usermanagement = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(usermanagement).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a UserManagement', () => {
      const usermanagement = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(usermanagement).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a UserManagement', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of UserManagement', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a UserManagement', () => {
      const expected = true;

      service.delete(123).subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    describe('addUserManagementToCollectionIfMissing', () => {
      it('should add a UserManagement to an empty array', () => {
        const usermanagement: IUserManagement = sampleWithRequiredData;
        expectedResult = service.addUserManagementToCollectionIfMissing([], usermanagement);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(usermanagement);
      });

      it('should not add a UserManagement to an array that contains it', () => {
        const usermanagement: IUserManagement = sampleWithRequiredData;
        const usermanagementCollection: IUserManagement[] = [
          {
            ...usermanagement,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addUserManagementToCollectionIfMissing(usermanagementCollection, usermanagement);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a UserManagement to an array that doesn't contain it", () => {
        const usermanagement: IUserManagement = sampleWithRequiredData;
        const usermanagementCollection: IUserManagement[] = [sampleWithPartialData];
        expectedResult = service.addUserManagementToCollectionIfMissing(usermanagementCollection, usermanagement);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(usermanagement);
      });

      it('should add only unique UserManagement to an array', () => {
        const usermanagementArray: IUserManagement[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const usermanagementCollection: IUserManagement[] = [sampleWithRequiredData];
        expectedResult = service.addUserManagementToCollectionIfMissing(usermanagementCollection, ...usermanagementArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const usermanagement: IUserManagement = sampleWithRequiredData;
        const usermanagement2: IUserManagement = sampleWithPartialData;
        expectedResult = service.addUserManagementToCollectionIfMissing([], usermanagement, usermanagement2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(usermanagement);
        expect(expectedResult).toContain(usermanagement2);
      });

      it('should accept null and undefined values', () => {
        const usermanagement: IUserManagement = sampleWithRequiredData;
        expectedResult = service.addUserManagementToCollectionIfMissing([], null, usermanagement, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(usermanagement);
      });

      it('should return initial array if no UserManagement is added', () => {
        const usermanagementCollection: IUserManagement[] = [sampleWithRequiredData];
        expectedResult = service.addUserManagementToCollectionIfMissing(usermanagementCollection, undefined, null);
        expect(expectedResult).toEqual(usermanagementCollection);
      });
    });

    describe('compareUserManagement', () => {
      it('Should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareUserManagement(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('Should return false if one entity is null', () => {
        const entity1 = { id: 123 };
        const entity2 = null;

        const compareResult1 = service.compareUserManagement(entity1, entity2);
        const compareResult2 = service.compareUserManagement(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey differs', () => {
        const entity1 = { id: 123 };
        const entity2 = { id: 456 };

        const compareResult1 = service.compareUserManagement(entity1, entity2);
        const compareResult2 = service.compareUserManagement(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey matches', () => {
        const entity1 = { id: 123 };
        const entity2 = { id: 123 };

        const compareResult1 = service.compareUserManagement(entity1, entity2);
        const compareResult2 = service.compareUserManagement(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
