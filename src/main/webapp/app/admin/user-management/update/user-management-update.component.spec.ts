import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subject, from, of } from 'rxjs';

import { UserManagementService } from '../service/user-management.service';
import { IUserManagement } from '../user-management.model';
import { UserManagementFormService } from './user-management-form.service';

import { UserManagementUpdateComponent } from './user-management-update.component';

describe('UserManagement Management Update Component', () => {
  let comp: UserManagementUpdateComponent;
  let fixture: ComponentFixture<UserManagementUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let usermanagementFormService: UserManagementFormService;
  let usermanagementService: UserManagementService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [UserManagementUpdateComponent],
      providers: [
        provideHttpClient(),
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(UserManagementUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(UserManagementUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    usermanagementFormService = TestBed.inject(UserManagementFormService);
    usermanagementService = TestBed.inject(UserManagementService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('Should update editForm', () => {
      const usermanagement: IUserManagement = { id: 456 };

      activatedRoute.data = of({ usermanagement });
      comp.ngOnInit();

      expect(comp.usermanagement).toEqual(usermanagement);
    });
  });

  describe('save', () => {
    it('Should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IUserManagement>>();
      const usermanagement = { id: 123 };
      jest.spyOn(usermanagementFormService, 'getUserManagement').mockReturnValue(usermanagement);
      jest.spyOn(usermanagementService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ usermanagement });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: usermanagement }));
      saveSubject.complete();

      // THEN
      expect(usermanagementFormService.getUserManagement).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(usermanagementService.update).toHaveBeenCalledWith(expect.objectContaining(usermanagement));
      expect(comp.isSaving).toEqual(false);
    });

    it('Should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IUserManagement>>();
      const usermanagement = { id: 123 };
      jest.spyOn(usermanagementFormService, 'getUserManagement').mockReturnValue({ id: null });
      jest.spyOn(usermanagementService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ usermanagement: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: usermanagement }));
      saveSubject.complete();

      // THEN
      expect(usermanagementFormService.getUserManagement).toHaveBeenCalled();
      expect(usermanagementService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('Should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IUserManagement>>();
      const usermanagement = { id: 123 };
      jest.spyOn(usermanagementService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ usermanagement });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(usermanagementService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });
});
