import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { IUserManagement, NewUserManagement } from '../user-management.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { id: unknown }> = Partial<Omit<T, 'id'>> & { id: T['id'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IUserManagement for edit and NewUserManagementFormGroupInput for create.
 */
type UserManagementFormGroupInput = IUserManagement | PartialWithRequiredKeyOf<NewUserManagement>;

type UserManagementFormDefaults = Pick<NewUserManagement, 'id' | 'enabled'>;

type UserManagementFormGroupContent = {
  id: FormControl<IUserManagement['id'] | NewUserManagement['id']>;
  username: FormControl<IUserManagement['username']>;
  firstName: FormControl<IUserManagement['firstName']>;
  lastName: FormControl<IUserManagement['lastName']>;
  email: FormControl<IUserManagement['email']>;
  enabled: FormControl<IUserManagement['enabled']>;
  roles: FormControl<IUserManagement['roles'] | string[]>;
  password: FormControl<IUserManagement['password']>;
};

export type UserManagementFormGroup = FormGroup<UserManagementFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class UserManagementFormService {
  createUserManagementFormGroup(usermanagement: UserManagementFormGroupInput = { id: null }): UserManagementFormGroup {
    const usermanagementRawValue = {
      ...this.getFormDefaults(),
      ...usermanagement,
    };
    return new FormGroup<UserManagementFormGroupContent>({
      id: new FormControl(
        { value: usermanagementRawValue.id, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      username: new FormControl(usermanagementRawValue.username),
      firstName: new FormControl(usermanagementRawValue.firstName),
      lastName: new FormControl(usermanagementRawValue.lastName),
      email: new FormControl(usermanagementRawValue.email),
      enabled: new FormControl(usermanagementRawValue.enabled),
      roles: new FormControl(usermanagementRawValue.roles),
      password: new FormControl(usermanagementRawValue.password),
    });
  }

  getUserManagement(form: UserManagementFormGroup): IUserManagement | NewUserManagement {
    return form.getRawValue() as IUserManagement | NewUserManagement;
  }

  resetForm(form: UserManagementFormGroup, usermanagement: UserManagementFormGroupInput): void {
    const usermanagementRawValue = { ...this.getFormDefaults(), ...usermanagement };
    form.reset(
      {
        ...usermanagementRawValue,
        id: { value: usermanagementRawValue.id, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): UserManagementFormDefaults {
    return {
      id: null,
      enabled: false,
    };
  }
}
