<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2
        id="jhi-usermanagement-heading"
        data-cy="UserManagementCreateUpdateHeading"
        jhiTranslate="legalReferentialFrontApp.usermanagement.home.createOrEditLabel"
      >
        Créer ou éditer un UserManagement
      </h2>

      <div>
        <jhi-alert-error></jhi-alert-error>

        @if (editForm.controls.id.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_id" jhiTranslate="legalReferentialFrontApp.usermanagement.id">ID</label>
            <input type="text" class="form-control" name="id" id="field_id" data-cy="id" formControlName="id" [readonly]="true" />
          </div>
        }

        <div class="mb-3">
          <label class="form-label" for="field_username" jhiTranslate="legalReferentialFrontApp.usermanagement.username">Username</label>
          <input type="text" class="form-control" name="username" id="field_username" data-cy="username" formControlName="username" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_firstName" jhiTranslate="legalReferentialFrontApp.usermanagement.firstName"
            >First Name</label
          >
          <input type="text" class="form-control" name="firstName" id="field_firstName" data-cy="firstName" formControlName="firstName" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_lastName" jhiTranslate="legalReferentialFrontApp.usermanagement.lastName">Last Name</label>
          <input type="text" class="form-control" name="lastName" id="field_lastName" data-cy="lastName" formControlName="lastName" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_email" jhiTranslate="legalReferentialFrontApp.usermanagement.email">Email</label>
          <input type="text" class="form-control" name="email" id="field_email" data-cy="email" formControlName="email" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_enabled" jhiTranslate="legalReferentialFrontApp.usermanagement.enabled">Enabled</label>
          <input type="checkbox" class="form-check" name="enabled" id="field_enabled" data-cy="enabled" formControlName="enabled" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_roles" jhiTranslate="legalReferentialFrontApp.usermanagement.roles">Roles</label>
          <select class="form-control" multiple name="authority" formControlName="roles" id="field_roles">
            <option
              *ngFor="let authority of authorities"
              [value]="authority"
              [selected]="isSelected(editForm.get('roles')!.value, authority)"
            >
              {{ authority.name }}
            </option>
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Sauvegarder</span>
        </button>
      </div>
    </form>
  </div>
</div>
