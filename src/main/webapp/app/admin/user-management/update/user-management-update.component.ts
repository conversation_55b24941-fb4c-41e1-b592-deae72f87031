import { Component, OnInit, inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IUserManagement } from '../user-management.model';
import { UserManagementService } from '../service/user-management.service';
import { UserManagementFormGroup, UserManagementFormService } from './user-management-form.service';

@Component({
  standalone: true,
  selector: 'jhi-usermanagement-update',
  templateUrl: './user-management-update.component.html',
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class UserManagementUpdateComponent implements OnInit {
  isSaving = false;
  usermanagement: IUserManagement | null = null;
  authorities: any[] = [];

  protected usermanagementService = inject(UserManagementService);
  protected usermanagementFormService = inject(UserManagementFormService);
  protected activatedRoute = inject(ActivatedRoute);
  protected userService: UserManagementService = inject(UserManagementService);

  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: UserManagementFormGroup = this.usermanagementFormService.createUserManagementFormGroup();

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ usermanagement }) => {
      this.usermanagement = usermanagement;
      if (usermanagement) {
        this.updateForm(usermanagement);
      }
    });
    this.userService.authorities().subscribe(authorities => {
      authorities.forEach(el => {
        const auth = {
          id: el.id,
          name: el.name,
        };
        this.authorities.push(auth);
      });
    });
  }

  previousState(): void {
    window.history.back();
  }

  save(): void {
    this.isSaving = true;
    const usermanagement = this.usermanagementFormService.getUserManagement(this.editForm);
    if (usermanagement.id !== null) {
      this.subscribeToSaveResponse(this.usermanagementService.update(usermanagement));
    } else {
      this.subscribeToSaveResponse(this.usermanagementService.create(usermanagement));
    }
  }

  isSelected(value: any, authority: any): boolean {
    if (!value) return false;
    return (value as any[]).some(el => el.name === authority.name);
  }

  protected subscribeToSaveResponse(result: Observable<HttpResponse<IUserManagement>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSaving = false;
  }

  protected updateForm(usermanagement: IUserManagement): void {
    this.usermanagement = usermanagement;
    this.usermanagementFormService.resetForm(this.editForm, usermanagement);
  }
}
