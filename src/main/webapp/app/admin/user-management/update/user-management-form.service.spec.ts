import { TestBed } from '@angular/core/testing';

import { sampleWithNewData, sampleWithRequiredData } from '../user-management.test-samples';

import { UserManagementFormService } from './user-management-form.service';

describe('UserManagement Form Service', () => {
  let service: UserManagementFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(UserManagementFormService);
  });

  describe('Service methods', () => {
    describe('createUserManagementFormGroup', () => {
      it('should create a new form with FormControl', () => {
        const formGroup = service.createUserManagementFormGroup();

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            id: expect.any(Object),
            username: expect.any(Object),
            firstName: expect.any(Object),
            lastName: expect.any(Object),
            email: expect.any(Object),
            enabled: expect.any(Object),
            roles: expect.any(Object),
            password: expect.any(Object),
          }),
        );
      });

      it('passing IUserManagement should create a new form with FormGroup', () => {
        const formGroup = service.createUserManagementFormGroup(sampleWithRequiredData);

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            id: expect.any(Object),
            username: expect.any(Object),
            firstName: expect.any(Object),
            lastName: expect.any(Object),
            email: expect.any(Object),
            enabled: expect.any(Object),
            roles: expect.any(Object),
            password: expect.any(Object),
          }),
        );
      });
    });

    describe('getUserManagement', () => {
      it('should return NewUserManagement for default UserManagement initial value', () => {
        const formGroup = service.createUserManagementFormGroup(sampleWithNewData);

        const usermanagement = service.getUserManagement(formGroup) as any;

        expect(usermanagement).toMatchObject(sampleWithNewData);
      });

      it('should return NewUserManagement for empty UserManagement initial value', () => {
        const formGroup = service.createUserManagementFormGroup();

        const usermanagement = service.getUserManagement(formGroup) as any;

        expect(usermanagement).toMatchObject({});
      });

      it('should return IUserManagement', () => {
        const formGroup = service.createUserManagementFormGroup(sampleWithRequiredData);

        const usermanagement = service.getUserManagement(formGroup) as any;

        expect(usermanagement).toMatchObject(sampleWithRequiredData);
      });
    });

    describe('resetForm', () => {
      it('passing IUserManagement should not enable id FormControl', () => {
        const formGroup = service.createUserManagementFormGroup();
        expect(formGroup.controls.id.disabled).toBe(true);

        service.resetForm(formGroup, sampleWithRequiredData);

        expect(formGroup.controls.id.disabled).toBe(true);
      });

      it('passing NewUserManagement should disable id FormControl', () => {
        const formGroup = service.createUserManagementFormGroup(sampleWithRequiredData);
        expect(formGroup.controls.id.disabled).toBe(true);

        service.resetForm(formGroup, { id: null });

        expect(formGroup.controls.id.disabled).toBe(true);
      });
    });
  });
});
