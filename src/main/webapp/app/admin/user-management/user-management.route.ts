import { Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import { ASC } from 'app/config/navigation.constants';
import UserManagementResolve from './route/user-management-routing-resolve.service';

const usermanagementRoute: Routes = [
  {
    path: '',
    loadComponent: () => import('./list/user-management.component').then(m => m.UserManagementComponent),
    data: {
      defaultSort: `id,${ASC}`,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/view',
    loadComponent: () => import('./detail/user-management-detail.component').then(m => m.UserManagementDetailComponent),
    resolve: {
      usermanagement: UserManagementResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    loadComponent: () => import('./update/user-management-update.component').then(m => m.UserManagementUpdateComponent),
    resolve: {
      usermanagement: UserManagementResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/edit',
    loadComponent: () => import('./update/user-management-update.component').then(m => m.UserManagementUpdateComponent),
    resolve: {
      usermanagement: UserManagementResolve,
    },
    canActivate: [UserRouteAccessService],
  },
];

export default usermanagementRoute;
