import { IUserManagement, NewUserManagement } from './user-management.model';

export const sampleWithRequiredData: IUserManagement = {
  id: 23753,
};

export const sampleWithPartialData: IUserManagement = {
  id: 9735,
  username: 'jusque',
  firstName: '<PERSON><PERSON><PERSON><PERSON>',
  lastName: '<PERSON>',
  enabled: true,
  password: 'avant guide meuh',
};

export const sampleWithFullData: IUserManagement = {
  id: 23195,
  username: 'ouah pendant que',
  firstName: 'Olympe',
  lastName: 'Roger',
  email: '<EMAIL>',
  enabled: false,
  roles: undefined,
  password: 'oups fonctionnaire',
};

export const sampleWithNewData: NewUserManagement = {
  id: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
