<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (usermanagement(); as usermanagementRef) {
      <div>
        <h2 data-cy="usermanagementDetailsHeading">
          <span jhiTranslate="legalReferentialFrontApp.usermanagement.detail.title">UserManagement</span>
        </h2>

        <hr />

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="legalReferentialFrontApp.usermanagement.login">Username</span></dt>
          <dd>
            <span>{{ usermanagementRef.username }}</span>
            <span *ngIf="usermanagementRef.enabled" class="badge bg-success" jhiTranslate="legalReferentialFrontApp.usermanagement.enabled"
              >Activé</span
            >
            <span
              *ngIf="!usermanagementRef.enabled"
              class="badge bg-danger"
              jhiTranslate="legalReferentialFrontApp.usermanagement.deactivated"
              >Désactivé</span
            >
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.usermanagement.firstName">First Name</span></dt>
          <dd>
            <span>{{ usermanagementRef.firstName }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.usermanagement.lastName">Last Name</span></dt>
          <dd>
            <span>{{ usermanagementRef.lastName }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.usermanagement.email">Email</span></dt>
          <dd>
            <span>{{ usermanagementRef.email }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.usermanagement.roles">Roles</span></dt>
          <dd>
            <span>{{ getlistRoles(usermanagementRef) }}</span>
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Retour</span>
        </button>
      </div>
    }
  </div>
</div>
