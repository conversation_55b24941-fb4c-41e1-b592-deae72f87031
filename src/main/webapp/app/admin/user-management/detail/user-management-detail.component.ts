import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { DurationPipe, FormatMediumDatePipe, FormatMediumDatetimePipe } from 'app/shared/date';
import { IUserManagement } from '../user-management.model';

@Component({
  standalone: true,
  selector: 'jhi-usermanagement-detail',
  templateUrl: './user-management-detail.component.html',
  imports: [SharedModule, RouterModule, DurationPipe, FormatMediumDatetimePipe, FormatMediumDatePipe],
})
export class UserManagementDetailComponent {
  usermanagement = input<IUserManagement | null>(null);

  previousState(): void {
    window.history.back();
  }

  getlistRoles(user: IUserManagement): string {
    let lstRoles = '';
    if (user.roles) {
      user.roles.forEach((role: string, index: number) => {
        if (index === 0) {
          lstRoles = role;
        } else {
          lstRoles = lstRoles + ', ' + role;
        }
      });
    }
    return lstRoles;
  }
}
