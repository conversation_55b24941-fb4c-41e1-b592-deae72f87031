<div>
  <h2 id="page-heading" data-cy="UserManagementHeading">
    <span jhiTranslate="legalReferentialFrontApp.usermanagement.home.title">UserManagements</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.usermanagement.home.refreshListLabel">Actualiser la liste</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-usermanagement"
        [routerLink]="['/admin/user-management/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.usermanagement.home.createLabel">Créer un nouveau UserManagement</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error></jhi-alert-error>

  <jhi-alert></jhi-alert>

  <jhi-filter [filters]="filters"></jhi-filter>

  @if (usermanagements?.length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="legalReferentialFrontApp.usermanagement.home.notFound">Aucun UserManagement trouvé</span>
    </div>
  }

  @if (usermanagements && usermanagements.length > 0) {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [sortState]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="id">
              <div class="d-flex">
                <span jhiTranslate="global.field.id">ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="login">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.usermanagement.email">Email</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="firstName">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.usermanagement.firstName">First Name</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="lastName">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.usermanagement.lastName">Last Name</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="enabled">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.usermanagement.enabled">Enabled</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="roles">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.usermanagement.roles">Roles</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (usermanagement of usermanagements; track trackId(usermanagement)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/admin/user-management', usermanagement.id, 'view']">{{ usermanagement.id }}</a>
              </td>
              <td>{{ usermanagement.email }}</td>
              <td>{{ usermanagement.firstName }}</td>
              <td>{{ usermanagement.lastName }}</td>
              <td>
                <button
                  class="btn btn-danger btn-sm"
                  (click)="setActive(usermanagement, true)"
                  *ngIf="!usermanagement.enabled"
                  jhiTranslate="legalReferentialFrontApp.usermanagement.deactivated"
                >
                  Désactivé
                </button>
                <button
                  class="btn btn-success btn-sm"
                  (click)="setActive(usermanagement, false)"
                  *ngIf="usermanagement.enabled"
                  [disabled]="!currentAccount || currentAccount.login === usermanagement.username"
                  jhiTranslate="legalReferentialFrontApp.usermanagement.activated"
                >
                  Activé
                </button>
              </td>
              <td>{{ getlistRoles(usermanagement) }}</td>
              <td class="text-end">
                <div class="btn-group">
                  <a
                    [routerLink]="['/admin/user-management', usermanagement.id, 'view']"
                    class="btn btn-info btn-sm"
                    data-cy="entityDetailsButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">Voir</span>
                  </a>

                  <a
                    [routerLink]="['/admin/user-management', usermanagement.id, 'edit']"
                    class="btn btn-primary btn-sm"
                    data-cy="entityEditButton"
                  >
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Editer</span>
                  </a>

                  <button type="submit" (click)="delete(usermanagement)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Supprimer</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
  @if (usermanagements && usermanagements.length > 0) {
    <div>
      <div class="d-flex justify-content-center">
        <jhi-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></jhi-item-count>
      </div>

      <div class="d-flex justify-content-center">
        <ngb-pagination
          [collectionSize]="totalItems"
          [page]="page"
          [pageSize]="itemsPerPage"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          (pageChange)="navigateToPage($event)"
        ></ngb-pagination>
      </div>
    </div>
  }
</div>
