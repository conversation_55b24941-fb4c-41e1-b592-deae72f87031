@if (usermanagement) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(usermanagement.id!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="usermanagementDeleteDialogHeading" jhiTranslate="entity.delete.title">
        Confirmation de suppression
      </h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error></jhi-alert-error>
      <p
        id="jhi-delete-usermanagement-heading"
        jhiTranslate="legalReferentialFrontApp.usermanagement.delete.question"
        [translateValues]="{ id: usermanagement.id }"
      >
        Êtes-vous certain de vouloir supprimer le UserManagement {{ usermanagement.id }} ?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
      </button>

      <button id="jhi-confirm-delete-usermanagement" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Supprimer</span>
      </button>
    </div>
  </form>
}
