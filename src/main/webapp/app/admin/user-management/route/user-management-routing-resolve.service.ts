import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IUserManagement } from '../user-management.model';
import { UserManagementService } from '../service/user-management.service';

const userManagementResolve = (route: ActivatedRouteSnapshot): Observable<null | IUserManagement> => {
  const id = route.params.id;
  if (id) {
    return inject(UserManagementService)
      .find(id)
      .pipe(
        mergeMap((usermanagement: HttpResponse<IUserManagement>) => {
          if (usermanagement.body) {
            return of(usermanagement.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default userManagementResolve;
