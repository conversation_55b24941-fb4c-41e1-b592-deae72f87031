<div>
  <h2>
    <span id="metrics-page-heading" data-cy="metricsPageHeading" jhiTranslate="metrics.title">Métriques de l&apos;application</span>

    <button class="btn btn-primary float-end" (click)="refresh()">
      <fa-icon icon="sync"></fa-icon> <span jhiTranslate="metrics.refresh.button">Rafraîchir</span>
    </button>
  </h2>

  <h3 jhiTranslate="metrics.jvm.title">Métriques de la JVM</h3>

  @let metricsRef = metrics();
  @if (metricsRef && !updatingMetrics()) {
    <div class="row">
      <jhi-jvm-memory class="col-md-4" [updating]="updatingMetrics()" [jvmMemoryMetrics]="metricsRef.jvm"></jhi-jvm-memory>

      <jhi-jvm-threads class="col-md-4" [threads]="threads()"></jhi-jvm-threads>

      <jhi-metrics-system class="col-md-4" [updating]="updatingMetrics()" [systemMetrics]="metricsRef.processMetrics"></jhi-metrics-system>
    </div>
  }

  @if (metricsRef?.garbageCollector; as metricsRefGarbageCollector) {
    <jhi-metrics-garbagecollector
      [updating]="updatingMetrics()"
      [garbageCollectorMetrics]="metricsRefGarbageCollector"
    ></jhi-metrics-garbagecollector>
  }

  @if (updatingMetrics()) {
    <div class="well well-lg" jhiTranslate="metrics.updating">Mise à jour...</div>
  }

  @if (metricsRef?.['http.server.requests']; as metricsRefHttpServerRequests) {
    <jhi-metrics-request [updating]="updatingMetrics()" [requestMetrics]="metricsRefHttpServerRequests"></jhi-metrics-request>
  }

  @if (metricsRef?.services; as metricsRefServices) {
    <jhi-metrics-endpoints-requests
      [updating]="updatingMetrics()"
      [endpointsRequestsMetrics]="metricsRefServices"
    ></jhi-metrics-endpoints-requests>
  }

  @if (metricsRef?.cache; as metricsRefCache) {
    <jhi-metrics-cache [updating]="updatingMetrics()" [cacheMetrics]="metricsRefCache"></jhi-metrics-cache>
  }

  @if (metricsRef && metricsKeyExistsAndObjectNotEmpty('databases')) {
    <jhi-metrics-datasource [updating]="updatingMetrics()" [datasourceMetrics]="metricsRef.databases"></jhi-metrics-datasource>
  }
</div>
