<div class="modal-header">
  <h4 class="modal-title" jhiTranslate="metrics.jvm.threads.dump.title">Threads dump</h4>

  <button type="button" class="btn-close" (click)="dismiss()">&nbsp;</button>
</div>

<div class="modal-body">
  <div class="mb-3">
    <button class="badge bg-primary hand" (click)="threadStateFilter = undefined" (keydown.enter)="threadStateFilter = undefined">
      @if (threadStateFilter === undefined) {
        <fa-icon icon="check"></fa-icon>
      }
      All&nbsp;<span class="badge rounded-pill bg-default">{{ threadDumpAll }}</span>
    </button>

    <button
      class="badge bg-success hand"
      (click)="threadStateFilter = ThreadState.Runnable"
      (keydown.enter)="threadStateFilter = ThreadState.Runnable"
    >
      @if (threadStateFilter === ThreadState.Runnable) {
        <fa-icon icon="check"></fa-icon>
      }
      Runnable&nbsp;<span class="badge rounded-pill bg-default">{{ threadDumpRunnable }}</span>
    </button>

    <button
      class="badge bg-info hand"
      (click)="threadStateFilter = ThreadState.Waiting"
      (keydown.enter)="threadStateFilter = ThreadState.Waiting"
    >
      @if (threadStateFilter === ThreadState.Waiting) {
        <fa-icon icon="check"></fa-icon>
      }
      Waiting&nbsp;<span class="badge rounded-pill bg-default">{{ threadDumpWaiting }}</span>
    </button>

    <button
      class="badge bg-warning hand"
      (click)="threadStateFilter = ThreadState.TimedWaiting"
      (keydown.enter)="threadStateFilter = ThreadState.TimedWaiting"
    >
      @if (threadStateFilter === ThreadState.TimedWaiting) {
        <fa-icon icon="check"></fa-icon>
      }
      Timed Waiting&nbsp;<span class="badge rounded-pill bg-default">{{ threadDumpTimedWaiting }}</span>
    </button>

    <button
      class="badge bg-danger hand"
      (click)="threadStateFilter = ThreadState.Blocked"
      (keydown.enter)="threadStateFilter = ThreadState.Blocked"
    >
      @if (threadStateFilter === ThreadState.Blocked) {
        <fa-icon icon="check"></fa-icon>
      }
      Blocked&nbsp;<span class="badge rounded-pill bg-default">{{ threadDumpBlocked }}</span>
    </button>
  </div>

  @for (thread of getThreads(); track $index) {
    <div class="pad">
      <h6>
        <span class="badge" [ngClass]="getBadgeClass(thread.threadState)">{{ thread.threadState }}</span>

        &nbsp;{{ thread.threadName }} (ID {{ thread.threadId }})

        <a (click)="thread.showThreadDump = !thread.showThreadDump" href="javascript:void(0);">
          <span [hidden]="thread.showThreadDump" jhiTranslate="metrics.jvm.threads.dump.show">Afficher</span>
          <span [hidden]="!thread.showThreadDump" jhiTranslate="metrics.jvm.threads.dump.hide">Cacher</span>
        </a>
      </h6>

      <div class="card" [hidden]="!thread.showThreadDump">
        <div class="card-body">
          @for (st of thread.stackTrace; track $index) {
            <div class="break">
              <samp
                >{{ st.className }}.{{ st.methodName }}(<code>{{ st.fileName }}:{{ st.lineNumber }}</code
                >)</samp
              >
              <span class="mt-1"></span>
            </div>
          }
        </div>
      </div>
      <table class="table table-sm table-responsive">
        <caption>
          Threads dump:
          {{
            thread.threadName
          }}
        </caption>
        <thead>
          <tr>
            <th scope="col" jhiTranslate="metrics.jvm.threads.dump.blockedtime">Temps bloqué</th>
            <th scope="col" jhiTranslate="metrics.jvm.threads.dump.blockedcount">Nb fois bloqué</th>
            <th scope="col" jhiTranslate="metrics.jvm.threads.dump.waitedtime">Temps d&apos;attente</th>
            <th scope="col" jhiTranslate="metrics.jvm.threads.dump.waitedcount">Nb fois en attente</th>
            <th scope="col" jhiTranslate="metrics.jvm.threads.dump.lockname">Nom du process bloquant</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{ thread.blockedTime }}</td>
            <td>{{ thread.blockedCount }}</td>
            <td>{{ thread.waitedTime }}</td>
            <td>{{ thread.waitedCount }}</td>
            <td class="thread-dump-modal-lock" title="{{ thread.lockName }}">
              <code>{{ thread.lockName }}</code>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  }
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary float-start" data-dismiss="modal" (click)="dismiss()">Done</button>
</div>
