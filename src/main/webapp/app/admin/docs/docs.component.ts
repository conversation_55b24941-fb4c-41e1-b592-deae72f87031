import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { ApplicationConfigService } from '../../core/config/application-config.service';

@Component({
  standalone: true,
  selector: 'jhi-docs',
  templateUrl: './docs.component.html',
  styleUrl: './docs.component.scss',
})
export default class DocsComponent {
  tokenValue: string | undefined = undefined;
  private readonly keycloak: KeycloakService = inject(KeycloakService);
  private applicationConfigService: ApplicationConfigService = inject(ApplicationConfigService);

  constructor() {
    this.keycloak.getToken().then(value => {
      this.tokenValue = value;
      sessionStorage.setItem('token', this.tokenValue);
      sessionStorage.setItem('backurl', this.applicationConfigService.getEndpointPrefix());
    });
  }
}
