<div>
  <h2>
    <span id="health-page-heading" data-cy="healthPageHeading" jhiTranslate="health.title">Diagnostics</span>

    <button class="btn btn-primary float-end" (click)="refresh()">
      <fa-icon icon="sync"></fa-icon> <span jhiTranslate="health.refresh.button"><PERSON><PERSON><PERSON><PERSON><PERSON></span>
    </button>
  </h2>

  <div class="table-responsive">
    <table id="healthCheck" class="table table-striped" aria-describedby="health-page-heading">
      <thead>
        <tr>
          <th scope="col" jhiTranslate="health.table.service">Nom du service</th>
          <th scope="col" class="text-center" jhiTranslate="health.table.status">État</th>
          <th scope="col" class="text-center" jhiTranslate="health.details.details">Détails</th>
        </tr>
      </thead>
      @if (health) {
        <tbody>
          @for (componentHealth of health.components | keyvalue; track componentHealth.key) {
            <tr>
              <td [jhiTranslate]="'health.indicator.' + componentHealth.key">
                {{
                  {
                    diskSpace: 'Espace disque',
                    mail: 'Email',
                    livenessState: 'Liveness state',
                    readinessState: 'Readiness state',
                    ping: 'Application',
                    db: 'Base de données',
                  }[componentHealth.key] || componentHealth.key
                }}
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="getBadgeClass(componentHealth.value!.status)"
                  [jhiTranslate]="'health.status.' + (componentHealth.value?.status ?? 'UNKNOWN')"
                >
                  {{
                    { UNKNOWN: 'INCONNU', UP: 'DISPONIBLE', OUT_OF_SERVICE: 'OUT_OF_SERVICE', DOWN: 'HORS SERVICE' }[
                      componentHealth.value?.status ?? 'UNKNOWN'
                    ]
                  }}
                </span>
              </td>
              <td class="text-center">
                @if (componentHealth.value!.details) {
                  <a class="hand" (click)="showHealth({ key: componentHealth.key, value: componentHealth.value! })">
                    <fa-icon icon="eye"></fa-icon>
                  </a>
                }
              </td>
            </tr>
          }
        </tbody>
      }
    </table>
  </div>
</div>
