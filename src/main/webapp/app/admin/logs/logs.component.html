@defer (when loggers() && !isLoading()) {
  <div class="table-responsive">
    <h2 id="logs-page-heading" data-cy="logsPageHeading" jhiTranslate="logs.title">Logs</h2>

    <p jhiTranslate="logs.nbloggers" [translateValues]="{ total: loggers()?.length }">
      Total de {{ loggers()?.length }} &quot;loggers&quot;.
    </p>

    <span jhiTranslate="logs.filter">Filtrer</span>
    <input type="text" [ngModel]="filter()" (ngModelChange)="filter.set($event)" class="form-control" />

    <table class="table table-sm table-striped table-bordered" aria-describedby="logs-page-heading">
      <thead>
        <tr jhiSort [sortState]="sortState" (sortChange)="sortState.set($event)">
          <th jhiSortBy="name" scope="col"><span jhiTranslate="logs.table.name">Nom</span> <fa-icon icon="sort"></fa-icon></th>
          <th jhiSortBy="level" scope="col"><span jhiTranslate="logs.table.level">Niveau</span> <fa-icon icon="sort"></fa-icon></th>
        </tr>
      </thead>

      <tbody>
        @for (logger of filteredAndOrderedLoggers(); track $index) {
          <tr>
            <td>
              <small>{{ logger.name | slice: 0 : 140 }}</small>
            </td>
            <td>
              <button
                (click)="changeLevel(logger.name, 'TRACE')"
                [ngClass]="logger.level === 'TRACE' ? 'btn-primary' : 'btn-light'"
                class="btn btn-sm"
              >
                TRACE
              </button>

              <button
                (click)="changeLevel(logger.name, 'DEBUG')"
                [ngClass]="logger.level === 'DEBUG' ? 'btn-success' : 'btn-light'"
                class="btn btn-sm"
              >
                DEBUG
              </button>

              <button
                (click)="changeLevel(logger.name, 'INFO')"
                [ngClass]="logger.level === 'INFO' ? 'btn-info' : 'btn-light'"
                class="btn btn-sm"
              >
                INFO
              </button>

              <button
                (click)="changeLevel(logger.name, 'WARN')"
                [ngClass]="logger.level === 'WARN' ? 'btn-warning' : 'btn-light'"
                class="btn btn-sm"
              >
                WARN
              </button>

              <button
                (click)="changeLevel(logger.name, 'ERROR')"
                [ngClass]="logger.level === 'ERROR' ? 'btn-danger' : 'btn-light'"
                class="btn btn-sm"
              >
                ERROR
              </button>

              <button
                (click)="changeLevel(logger.name, 'OFF')"
                [ngClass]="logger.level === 'OFF' ? 'btn-secondary' : 'btn-light'"
                class="btn btn-sm"
              >
                OFF
              </button>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
} @loading {
  <div class="d-flex justify-content-center me-3">
    <div class="spinner-border"></div>
  </div>
}
