<nav data-cy="navbar" class="navbar navbar-dark navbar-expand-md bg-dark">
  <div class="container-fluid">
    <a class="navbar-brand logo" routerLink="/" (click)="collapseNavbar()">
      <span class="logo-img"></span>
      <span class="navbar-title" jhiTranslate="global.title">LegalReferentialFront</span>
      <span class="navbar-version">{{ version }}</span>
    </a>
    <a
      class="navbar-toggler d-lg-none"
      href="javascript:void(0);"
      data-toggle="collapse"
      data-target="#navbarResponsive"
      aria-controls="navbarResponsive"
      aria-expanded="false"
      aria-label="Toggle navigation"
      (click)="toggleNavbar()"
    >
      <fa-icon icon="bars"></fa-icon>
    </a>
    <div class="navbar-collapse collapse" id="navbarResponsive" [ngbCollapse]="isNavbarCollapsed()">
      <ul class="navbar-nav ms-auto">
        <li class="nav-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }">
          <a class="nav-link" routerLink="/" (click)="collapseNavbar()">
            <span>
              <fa-icon icon="home"></fa-icon>
              <span jhiTranslate="global.menu.home">Accueil</span>
            </span>
          </a>
        </li>
        <!-- jhipster-needle-add-element-to-menu - JHipster will add new menu items here -->
        @if (account() !== null) {
          <li
            ngbDropdown
            class="nav-item dropdown pointer"
            display="dynamic"
            routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }"
          >
            <a class="nav-link dropdown-toggle" ngbDropdownToggle href="javascript:void(0);" id="entity-menu" data-cy="entity">
              <span>
                <fa-icon icon="th-list"></fa-icon>
                <span jhiTranslate="global.menu.entities.main">Entités</span>
              </span>
            </a>
            <ul class="dropdown-menu" ngbDropdownMenu aria-labelledby="entity-menu">
              <li>
                <a
                  class="dropdown-item"
                  routerLink="/siren"
                  routerLinkActive="active"
                  [routerLinkActiveOptions]="{ exact: true }"
                  (click)="collapseNavbar()"
                >
                  <fa-icon icon="asterisk" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.entities.siren">Siren</span>
                </a>
              </li>
              <li>
                <a
                  class="dropdown-item"
                  routerLink="/siret"
                  routerLinkActive="active"
                  [routerLinkActiveOptions]="{ exact: true }"
                  (click)="collapseNavbar()"
                >
                  <fa-icon icon="asterisk" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.entities.siret">Siret</span>
                </a>
              </li>
              <li>
                <a
                  class="dropdown-item"
                  routerLink="/routing-code"
                  routerLinkActive="active"
                  [routerLinkActiveOptions]="{ exact: true }"
                  (click)="collapseNavbar()"
                >
                  <fa-icon icon="asterisk" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.entities.routingCode">Routing Code</span>
                </a>
              </li>
              <li>
                <a
                  class="dropdown-item"
                  routerLink="/address-line"
                  routerLinkActive="active"
                  [routerLinkActiveOptions]="{ exact: true }"
                  (click)="collapseNavbar()"
                >
                  <fa-icon icon="asterisk" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.entities.addressLine">Address Line</span>
                </a>
              </li>
              <li>
                <a
                  class="dropdown-item"
                  routerLink="/platform"
                  routerLinkActive="active"
                  [routerLinkActiveOptions]="{ exact: true }"
                  (click)="collapseNavbar()"
                >
                  <fa-icon icon="asterisk" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.entities.platform">Platform</span>
                </a>
              </li>
              <!-- jhipster-needle-add-entity-to-menu - JHipster will add entities to the menu here -->
            </ul>
          </li>
        }
        <li
          *jhiHasAnyAuthority="'LEGALREF_ADMIN'"
          ngbDropdown
          class="nav-item dropdown pointer"
          display="dynamic"
          routerLinkActive="active"
          [routerLinkActiveOptions]="{ exact: true }"
        >
          <a class="nav-link dropdown-toggle" ngbDropdownToggle href="javascript:void(0);" id="admin-menu" data-cy="adminMenu">
            <span>
              <fa-icon icon="users-cog"></fa-icon>
              <span jhiTranslate="global.menu.admin.main">Administration</span>
            </span>
          </a>
          <ul class="dropdown-menu" ngbDropdownMenu aria-labelledby="admin-menu">
            <!-- jhipster-needle-add-element-to-admin-menu - JHipster will add entities to the admin menu here -->
            <li>
              <a class="dropdown-item" routerLink="/admin/user-management" routerLinkActive="active" (click)="collapseNavbar()">
                <fa-icon icon="users" [fixedWidth]="true"></fa-icon>
                <span jhiTranslate="global.menu.admin.userManagement">Gestion des utilisateurs</span>
              </a>
            </li>
            <li>
              <a class="dropdown-item" routerLink="/admin/metrics" routerLinkActive="active" (click)="collapseNavbar()">
                <fa-icon icon="tachometer-alt" [fixedWidth]="true"></fa-icon>
                <span jhiTranslate="global.menu.admin.metrics">Métriques</span>
              </a>
            </li>
            <li>
              <a class="dropdown-item" routerLink="/admin/health" routerLinkActive="active" (click)="collapseNavbar()">
                <fa-icon icon="heart" [fixedWidth]="true"></fa-icon>
                <span jhiTranslate="global.menu.admin.health">Diagnostics</span>
              </a>
            </li>
            <li>
              <a class="dropdown-item" routerLink="/admin/configuration" routerLinkActive="active" (click)="collapseNavbar()">
                <fa-icon icon="cogs" [fixedWidth]="true"></fa-icon>
                <span jhiTranslate="global.menu.admin.configuration">Configuration</span>
              </a>
            </li>
            <li>
              <a class="dropdown-item" routerLink="/admin/logs" routerLinkActive="active" (click)="collapseNavbar()">
                <fa-icon icon="tasks" [fixedWidth]="true"></fa-icon>
                <span jhiTranslate="global.menu.admin.logs">Logs</span>
              </a>
            </li>
            @if (openAPIEnabled) {
              <li>
                <a class="dropdown-item" routerLink="/admin/docs" routerLinkActive="active" (click)="collapseNavbar()">
                  <fa-icon icon="book" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.admin.apidocs">API</span>
                </a>
              </li>
            }
          </ul>
        </li>
        @if (languages && languages.length > 1) {
          <li ngbDropdown class="nav-item dropdown pointer" display="dynamic">
            <a class="nav-link dropdown-toggle" ngbDropdownToggle href="javascript:void(0);" id="languagesnavBarDropdown">
              <span>
                <fa-icon icon="flag"></fa-icon>
                <span jhiTranslate="global.menu.language">Langue</span>
              </span>
            </a>
            <ul class="dropdown-menu" ngbDropdownMenu aria-labelledby="languagesnavBarDropdown">
              @for (language of languages; track $index) {
                <li>
                  <a
                    class="dropdown-item"
                    [jhiActiveMenu]="language"
                    href="javascript:void(0);"
                    (click)="changeLanguage(language); collapseNavbar()"
                    >{{ language | findLanguageFromKey }}</a
                  >
                </li>
              }
            </ul>
          </li>
        }
        <li
          ngbDropdown
          class="nav-item dropdown pointer"
          display="dynamic"
          routerLinkActive="active"
          [routerLinkActiveOptions]="{ exact: true }"
        >
          @let accountRef = account();
          <a class="nav-link dropdown-toggle" ngbDropdownToggle href="javascript:void(0);" id="account-menu" data-cy="accountMenu">
            @if (!accountRef?.imageUrl) {
              <span>
                <fa-icon icon="user"></fa-icon>
                <span jhiTranslate="global.menu.account.main">Compte</span>
              </span>
            } @else {
              <span>
                <img [src]="accountRef!.imageUrl" class="profile-image rounded-circle" alt="Avatar" />
              </span>
            }
          </a>
          <ul class="dropdown-menu" ngbDropdownMenu aria-labelledby="account-menu">
            @if (accountRef !== null) {
              <li>
                <a class="dropdown-item" (click)="logout()" id="logout" data-cy="logout">
                  <fa-icon icon="sign-out-alt" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.account.logout">Déconnexion</span>
                </a>
              </li>
            } @else {
              <li>
                <a class="dropdown-item" (click)="login()" id="login" data-cy="login">
                  <fa-icon icon="sign-in-alt" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.account.login">S&apos;authentifier</span>
                </a>
              </li>
              <li>
                <a
                  class="dropdown-item"
                  routerLink="/account/register"
                  routerLinkActive="active"
                  (click)="collapseNavbar()"
                  data-cy="register"
                >
                  <fa-icon icon="user-plus" [fixedWidth]="true"></fa-icon>
                  <span jhiTranslate="global.menu.account.register">Créer un compte</span>
                </a>
              </li>
            }
          </ul>
        </li>
      </ul>
    </div>
  </div>
</nav>
