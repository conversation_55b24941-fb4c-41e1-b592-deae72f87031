import { IUser } from './user.model';

export const sampleWithRequiredData: IUser = {
  id: '6179',
  login: '-hGI@jGZ\\)Ln\\Ek8ms\\axhhbJ\\_VcL\\u9U',
};

export const sampleWithPartialData: IUser = {
  id: '26875',
  login: 'PWKxD',
};

export const sampleWithFullData: IUser = {
  id: '4134',
  login: 'Vm@@k',
};
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
