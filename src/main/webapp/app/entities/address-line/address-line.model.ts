import dayjs from 'dayjs/esm';
import { IRoutingCode } from 'app/entities/routing-code/routing-code.model';
import { ISiren } from 'app/entities/siren/siren.model';
import { ISiret } from 'app/entities/siret/siret.model';
import { Nature } from 'app/entities/enumerations/nature.model';

export interface IAddressLine {
  instanceID: string;
  addressLineID?: string | null;
  addressSuffix?: string | null;
  effectBeginDate?: dayjs.Dayjs | null;
  effectEndDate?: dayjs.Dayjs | null;
  effectiveEndDate?: dayjs.Dayjs | null;
  nature?: keyof typeof Nature | null;
  creationDate?: dayjs.Dayjs | null;
  creationBy?: string | null;
  lastModificationDate?: dayjs.Dayjs | null;
  modifiedBy?: string | null;
  fkAddressLineRoutingCode?: Pick<IRoutingCode, 'instanceID'> | null;
  fkAddressLineSiren?: Pick<ISiren, 'instanceID'> | null;
  fkAddressLineSiret?: Pick<ISiret, 'instanceID'> | null;
}

export type NewAddressLine = Omit<IAddressLine, 'instanceID'> & { instanceID: null };
