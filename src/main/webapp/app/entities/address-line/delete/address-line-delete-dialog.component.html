@if (addressLine) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(addressLine.instanceID!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="addressLineDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirmation de suppression</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error></jhi-alert-error>
      <p
        id="jhi-delete-addressLine-heading"
        jhiTranslate="legalReferentialFrontApp.addressLine.delete.question"
        [translateValues]="{ instanceID: addressLine.instanceID }"
      >
        Êtes-vous certain de vouloir supprimer le Address Line {{ addressLine.instanceID }} ?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
      </button>

      <button id="jhi-confirm-delete-addressLine" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Supprimer</span>
      </button>
    </div>
  </form>
}
