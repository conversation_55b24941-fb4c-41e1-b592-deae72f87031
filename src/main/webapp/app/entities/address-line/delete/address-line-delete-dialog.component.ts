import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import SharedModule from 'app/shared/shared.module';
import { ITEM_DELETED_EVENT } from 'app/config/navigation.constants';
import { IAddressLine } from '../address-line.model';
import { AddressLineService } from '../service/address-line.service';

@Component({
  standalone: true,
  templateUrl: './address-line-delete-dialog.component.html',
  imports: [SharedModule, FormsModule],
})
export class AddressLineDeleteDialogComponent {
  addressLine?: IAddressLine;

  protected addressLineService = inject(AddressLineService);
  protected activeModal = inject(NgbActiveModal);

  cancel(): void {
    this.activeModal.dismiss();
  }

  confirmDelete(instanceID: string): void {
    this.addressLineService.delete(instanceID).subscribe(() => {
      this.activeModal.close(ITEM_DELETED_EVENT);
    });
  }
}
