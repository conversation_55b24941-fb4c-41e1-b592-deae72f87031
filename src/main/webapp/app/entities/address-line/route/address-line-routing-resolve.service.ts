import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IAddressLine } from '../address-line.model';
import { AddressLineService } from '../service/address-line.service';

const addressLineResolve = (route: ActivatedRouteSnapshot): Observable<null | IAddressLine> => {
  const instanceID = route.params.instanceID;
  if (instanceID) {
    return inject(AddressLineService)
      .find(instanceID)
      .pipe(
        mergeMap((addressLine: HttpResponse<IAddressLine>) => {
          if (addressLine.body) {
            return of(addressLine.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default addressLineResolve;
