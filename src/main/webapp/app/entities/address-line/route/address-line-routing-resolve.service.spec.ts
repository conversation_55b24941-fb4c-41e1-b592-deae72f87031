import { TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { ActivatedRoute, ActivatedRouteSnapshot, Router, convertToParamMap } from '@angular/router';
import { of } from 'rxjs';

import { IAddressLine } from '../address-line.model';
import { AddressLineService } from '../service/address-line.service';

import addressLineResolve from './address-line-routing-resolve.service';

describe('AddressLine routing resolve service', () => {
  let mockRouter: Router;
  let mockActivatedRouteSnapshot: ActivatedRouteSnapshot;
  let service: AddressLineService;
  let resultAddressLine: IAddressLine | null | undefined;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: convertToParamMap({}),
            },
          },
        },
      ],
    });
    mockRouter = TestBed.inject(Router);
    jest.spyOn(mockRouter, 'navigate').mockImplementation(() => Promise.resolve(true));
    mockActivatedRouteSnapshot = TestBed.inject(ActivatedRoute).snapshot;
    service = TestBed.inject(AddressLineService);
    resultAddressLine = undefined;
  });

  describe('resolve', () => {
    it('should return IAddressLine returned by find', () => {
      // GIVEN
      service.find = jest.fn(instanceID => of(new HttpResponse({ body: { instanceID } })));
      mockActivatedRouteSnapshot.params = { instanceID: '123' };

      // WHEN
      TestBed.runInInjectionContext(() => {
        addressLineResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultAddressLine = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultAddressLine).toEqual({ instanceID: 123 });
    });

    it('should return null if id is not provided', () => {
      // GIVEN
      service.find = jest.fn();
      mockActivatedRouteSnapshot.params = {};

      // WHEN
      TestBed.runInInjectionContext(() => {
        addressLineResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultAddressLine = result;
          },
        });
      });

      // THEN
      expect(service.find).not.toHaveBeenCalled();
      expect(resultAddressLine).toEqual(null);
    });

    it('should route to 404 page if data not found in server', () => {
      // GIVEN
      jest.spyOn(service, 'find').mockReturnValue(of(new HttpResponse<IAddressLine>({ body: null })));
      mockActivatedRouteSnapshot.params = { instanceID: 123 };

      // WHEN
      TestBed.runInInjectionContext(() => {
        addressLineResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultAddressLine = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultAddressLine).toEqual(undefined);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['404']);
    });
  });
});
