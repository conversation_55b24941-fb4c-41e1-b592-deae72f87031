<div>
  <h2 id="page-heading" data-cy="AddressLineHeading">
    <span jhiTranslate="legalReferentialFrontApp.addressLine.home.title">AddressLines</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.addressLine.home.refreshListLabel">Actualiser la liste</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-address-line"
        [routerLink]="['/address-line/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.addressLine.home.createLabel">Créer un nouveau Address Line</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error></jhi-alert-error>

  <jhi-alert></jhi-alert>

  <jhi-filter [filters]="filters"></jhi-filter>

  @if (addressLines?.length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="legalReferentialFrontApp.addressLine.home.notFound">Aucun Address Line trouvé</span>
    </div>
  }

  @if (addressLines && addressLines.length > 0) {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [sortState]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="instanceID">
              <div class="d-flex">
                <span jhiTranslate="global.field.instanceID">ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>

            <th scope="col" jhiSortBy="addressLineID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.addressLineID">Address Line ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressSuffix">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.addressSuffix">Address Suffix</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="effectBeginDate">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.effectBeginDate">Effect Begin Date</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="effectEndDate">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.effectEndDate">Effect End Date</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="effectiveEndDate">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.effectiveEndDate">Effective End Date</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="nature">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.nature">Nature</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="creationDate">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.creationDate">Creation Date</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="creationBy">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.creationBy">Creation By</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="lastModificationDate">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.lastModificationDate">Last Modification Date</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="modifiedBy">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.modifiedBy">Modified By</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="fkAddressLineRoutingCode.instanceID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineRoutingCode">Pk Routing Code</span>
                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="fkAddressLineSiren.instanceID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineSiren">Fk Address Line Siren</span>
                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="fkAddressLineSiret.instanceID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineSiret">Fk Address Line Siret</span>
                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (addressLine of addressLines; track trackId(addressLine)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/address-line', addressLine.instanceID, 'view']">{{ addressLine.instanceID }}</a>
              </td>
              <td>{{ addressLine.addressLineID }}</td>
              <td>{{ addressLine.addressSuffix }}</td>
              <td>{{ addressLine.effectBeginDate | formatMediumDate }}</td>
              <td>{{ addressLine.effectEndDate | formatMediumDate }}</td>
              <td>{{ addressLine.effectiveEndDate | formatMediumDate }}</td>
              <td [jhiTranslate]="'legalReferentialFrontApp.Nature.' + (addressLine.nature ?? 'null')">
                {{ { null: '', M: 'M', D: 'D' }[addressLine.nature ?? 'null'] }}
              </td>
              <td>{{ addressLine.creationDate | formatMediumDate }}</td>
              <td>{{ addressLine.creationBy }}</td>
              <td>{{ addressLine.lastModificationDate | formatMediumDatetime }}</td>
              <td>{{ addressLine.modifiedBy }}</td>
              <td>
                @if (addressLine.fkAddressLineRoutingCode) {
                  <div>
                    <a [routerLink]="['/routing-code', addressLine.fkAddressLineRoutingCode.instanceID, 'view']">{{
                      addressLine.fkAddressLineRoutingCode.instanceID
                    }}</a>
                  </div>
                }
              </td>
              <td>
                @if (addressLine.fkAddressLineSiren) {
                  <div>
                    <a [routerLink]="['/siren', addressLine.fkAddressLineSiren.instanceID, 'view']">{{
                      addressLine.fkAddressLineSiren.instanceID
                    }}</a>
                  </div>
                }
              </td>
              <td>
                @if (addressLine.fkAddressLineSiret) {
                  <div>
                    <a [routerLink]="['/siret', addressLine.fkAddressLineSiret.instanceID, 'view']">{{
                      addressLine.fkAddressLineSiret.instanceID
                    }}</a>
                  </div>
                }
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <a
                    [routerLink]="['/address-line', addressLine.instanceID, 'view']"
                    class="btn btn-info btn-sm"
                    data-cy="entityDetailsButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">Voir</span>
                  </a>

                  <a
                    [routerLink]="['/address-line', addressLine.instanceID, 'edit']"
                    class="btn btn-primary btn-sm"
                    data-cy="entityEditButton"
                  >
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Editer</span>
                  </a>

                  <button type="submit" (click)="delete(addressLine)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Supprimer</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
  @if (addressLines && addressLines.length > 0) {
    <div>
      <div class="d-flex justify-content-center">
        <jhi-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></jhi-item-count>
      </div>

      <div class="d-flex justify-content-center">
        <ngb-pagination
          [collectionSize]="totalItems"
          [page]="page"
          [pageSize]="itemsPerPage"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          (pageChange)="navigateToPage($event)"
        ></ngb-pagination>
      </div>
    </div>
  }
</div>
