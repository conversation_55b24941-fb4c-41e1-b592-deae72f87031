import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';

import { DATE_FORMAT } from 'app/config/input.constants';
import { IAddressLine } from '../address-line.model';
import { sampleWithFullData, sampleWithNewData, sampleWithPartialData, sampleWithRequiredData } from '../address-line.test-samples';

import { AddressLineService, RestAddressLine } from './address-line.service';

const requireRestSample: RestAddressLine = {
  ...sampleWithRequiredData,
  effectBeginDate: sampleWithRequiredData.effectBeginDate?.format(DATE_FORMAT),
  effectEndDate: sampleWithRequiredData.effectEndDate?.format(DATE_FORMAT),
  effectiveEndDate: sampleWithRequiredData.effectiveEndDate?.format(DATE_FORMAT),
  creationDate: sampleWithRequiredData.creationDate?.format(DATE_FORMAT),
  lastModificationDate: sampleWithRequiredData.lastModificationDate?.toJSON(),
};

describe('AddressLine Service', () => {
  let service: AddressLineService;
  let httpMock: HttpTestingController;
  let expectedResult: IAddressLine | IAddressLine[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    expectedResult = null;
    service = TestBed.inject(AddressLineService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find('123').subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a AddressLine', () => {
      const addressLine = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(addressLine).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a AddressLine', () => {
      const addressLine = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(addressLine).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a AddressLine', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of AddressLine', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a AddressLine', () => {
      const expected = true;

      service.delete('123').subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    describe('addAddressLineToCollectionIfMissing', () => {
      it('should add a AddressLine to an empty array', () => {
        const addressLine: IAddressLine = sampleWithRequiredData;
        expectedResult = service.addAddressLineToCollectionIfMissing([], addressLine);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(addressLine);
      });

      it('should not add a AddressLine to an array that contains it', () => {
        const addressLine: IAddressLine = sampleWithRequiredData;
        const addressLineCollection: IAddressLine[] = [
          {
            ...addressLine,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addAddressLineToCollectionIfMissing(addressLineCollection, addressLine);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a AddressLine to an array that doesn't contain it", () => {
        const addressLine: IAddressLine = sampleWithRequiredData;
        const addressLineCollection: IAddressLine[] = [sampleWithPartialData];
        expectedResult = service.addAddressLineToCollectionIfMissing(addressLineCollection, addressLine);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(addressLine);
      });

      it('should add only unique AddressLine to an array', () => {
        const addressLineArray: IAddressLine[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const addressLineCollection: IAddressLine[] = [sampleWithRequiredData];
        expectedResult = service.addAddressLineToCollectionIfMissing(addressLineCollection, ...addressLineArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const addressLine: IAddressLine = sampleWithRequiredData;
        const addressLine2: IAddressLine = sampleWithPartialData;
        expectedResult = service.addAddressLineToCollectionIfMissing([], addressLine, addressLine2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(addressLine);
        expect(expectedResult).toContain(addressLine2);
      });

      it('should accept null and undefined values', () => {
        const addressLine: IAddressLine = sampleWithRequiredData;
        expectedResult = service.addAddressLineToCollectionIfMissing([], null, addressLine, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(addressLine);
      });

      it('should return initial array if no AddressLine is added', () => {
        const addressLineCollection: IAddressLine[] = [sampleWithRequiredData];
        expectedResult = service.addAddressLineToCollectionIfMissing(addressLineCollection, undefined, null);
        expect(expectedResult).toEqual(addressLineCollection);
      });
    });

    describe('compareAddressLine', () => {
      it('Should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareAddressLine(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('Should return false if one entity is null', () => {
        const entity1 = { instanceID: '123' };
        const entity2 = null;

        const compareResult1 = service.compareAddressLine(entity1, entity2);
        const compareResult2 = service.compareAddressLine(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey differs', () => {
        const entity1 = { instanceID: '123' };
        const entity2 = { instanceID: '456' };

        const compareResult1 = service.compareAddressLine(entity1, entity2);
        const compareResult2 = service.compareAddressLine(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey matches', () => {
        const entity1 = { instanceID: '123' };
        const entity2 = { instanceID: '123' };

        const compareResult1 = service.compareAddressLine(entity1, entity2);
        const compareResult2 = service.compareAddressLine(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
