import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, map } from 'rxjs';

import dayjs from 'dayjs/esm';

import { isPresent } from 'app/core/util/operators';
import { DATE_FORMAT } from 'app/config/input.constants';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { IAddressLine, NewAddressLine } from '../address-line.model';

export type PartialUpdateAddressLine = Partial<IAddressLine> & Pick<IAddressLine, 'instanceID'>;

type RestOf<T extends IAddressLine | NewAddressLine> = Omit<
  T,
  'effectBeginDate' | 'effectEndDate' | 'effectiveEndDate' | 'creationDate' | 'lastModificationDate'
> & {
  effectBeginDate?: string | null;
  effectEndDate?: string | null;
  effectiveEndDate?: string | null;
  creationDate?: string | null;
  lastModificationDate?: string | null;
};

export type RestAddressLine = RestOf<IAddressLine>;

export type NewRestAddressLine = RestOf<NewAddressLine>;

export type PartialUpdateRestAddressLine = RestOf<PartialUpdateAddressLine>;

export type EntityResponseType = HttpResponse<IAddressLine>;
export type EntityArrayResponseType = HttpResponse<IAddressLine[]>;

@Injectable({ providedIn: 'root' })
export class AddressLineService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/address-lines');

  create(addressLine: NewAddressLine): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(addressLine);
    return this.http
      .post<RestAddressLine>(this.resourceUrl, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  update(addressLine: IAddressLine): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(addressLine);
    return this.http
      .put<RestAddressLine>(`${this.resourceUrl}/${this.getAddressLineIdentifier(addressLine)}`, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  partialUpdate(addressLine: PartialUpdateAddressLine): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(addressLine);
    return this.http
      .patch<RestAddressLine>(`${this.resourceUrl}/${this.getAddressLineIdentifier(addressLine)}`, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  find(instanceID: string): Observable<EntityResponseType> {
    return this.http
      .get<RestAddressLine>(`${this.resourceUrl}/${instanceID}`, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestAddressLine[]>(this.resourceUrl, { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }

  delete(instanceID: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  getAddressLineIdentifier(addressLine: Pick<IAddressLine, 'instanceID'>): string {
    return addressLine.instanceID;
  }

  compareAddressLine(o1: Pick<IAddressLine, 'instanceID'> | null, o2: Pick<IAddressLine, 'instanceID'> | null): boolean {
    return o1 && o2 ? this.getAddressLineIdentifier(o1) === this.getAddressLineIdentifier(o2) : o1 === o2;
  }

  addAddressLineToCollectionIfMissing<Type extends Pick<IAddressLine, 'instanceID'>>(
    addressLineCollection: Type[],
    ...addressLinesToCheck: (Type | null | undefined)[]
  ): Type[] {
    const addressLines: Type[] = addressLinesToCheck.filter(isPresent);
    if (addressLines.length > 0) {
      const addressLineCollectionIdentifiers = addressLineCollection.map(addressLineItem => this.getAddressLineIdentifier(addressLineItem));
      const addressLinesToAdd = addressLines.filter(addressLineItem => {
        const addressLineIdentifier = this.getAddressLineIdentifier(addressLineItem);
        if (addressLineCollectionIdentifiers.includes(addressLineIdentifier)) {
          return false;
        }
        addressLineCollectionIdentifiers.push(addressLineIdentifier);
        return true;
      });
      return [...addressLinesToAdd, ...addressLineCollection];
    }
    return addressLineCollection;
  }

  protected convertDateFromClient<T extends IAddressLine | NewAddressLine | PartialUpdateAddressLine>(addressLine: T): RestOf<T> {
    return {
      ...addressLine,
      effectBeginDate: addressLine.effectBeginDate?.format(DATE_FORMAT) ?? null,
      effectEndDate: addressLine.effectEndDate?.format(DATE_FORMAT) ?? null,
      effectiveEndDate: addressLine.effectiveEndDate?.format(DATE_FORMAT) ?? null,
      creationDate: addressLine.creationDate?.format(DATE_FORMAT) ?? null,
      lastModificationDate: addressLine.lastModificationDate?.toJSON() ?? null,
    };
  }

  protected convertDateFromServer(restAddressLine: RestAddressLine): IAddressLine {
    return {
      ...restAddressLine,
      effectBeginDate: restAddressLine.effectBeginDate ? dayjs(restAddressLine.effectBeginDate) : undefined,
      effectEndDate: restAddressLine.effectEndDate ? dayjs(restAddressLine.effectEndDate) : undefined,
      effectiveEndDate: restAddressLine.effectiveEndDate ? dayjs(restAddressLine.effectiveEndDate) : undefined,
      creationDate: restAddressLine.creationDate ? dayjs(restAddressLine.creationDate) : undefined,
      lastModificationDate: restAddressLine.lastModificationDate ? dayjs(restAddressLine.lastModificationDate) : undefined,
    };
  }

  protected convertResponseFromServer(res: HttpResponse<RestAddressLine>): HttpResponse<IAddressLine> {
    return res.clone({
      body: res.body ? this.convertDateFromServer(res.body) : null,
    });
  }

  protected convertResponseArrayFromServer(res: HttpResponse<RestAddressLine[]>): HttpResponse<IAddressLine[]> {
    return res.clone({
      body: res.body ? res.body.map(item => this.convertDateFromServer(item)) : null,
    });
  }
}
