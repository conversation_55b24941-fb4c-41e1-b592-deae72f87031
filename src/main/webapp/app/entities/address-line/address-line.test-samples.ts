import dayjs from 'dayjs/esm';

import { IAddressLine, NewAddressLine } from './address-line.model';

export const sampleWithRequiredData: IAddressLine = {
  instanceID: 20701,
};

export const sampleWithPartialData: IAddressLine = {
  instanceID: 13401,
  addressLineID: 'tant',
  effectBeginDate: dayjs('2023-09-27'),
  effectEndDate: dayjs('2023-09-27'),
  nature: 'M',
  creationDate: dayjs('2023-09-27'),
  lastModificationDate: dayjs('2023-09-27T16:46'),
  modifiedBy: 'soumettre énumérer',
};

export const sampleWithFullData: IAddressLine = {
  instanceID: 1396,
  addressLineID: "naguère à l'égard de habile",
  addressSuffix: 'ha',
  effectBeginDate: dayjs('2023-09-27'),
  effectEndDate: dayjs('2023-09-27'),
  effectiveEndDate: dayjs('2023-09-28'),
  nature: 'D',
  creationDate: dayjs('2023-09-27'),
  creationBy: 'partenaire avant',
  lastModificationDate: dayjs('2023-09-27T15:10'),
  modifiedBy: 'diététiste affable',
};

export const sampleWithNewData: NewAddressLine = {
  instanceID: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
