import { Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import { ASC } from 'app/config/navigation.constants';
import AddressLineResolve from './route/address-line-routing-resolve.service';

const addressLineRoute: Routes = [
  {
    path: '',
    loadComponent: () => import('./list/address-line.component').then(m => m.AddressLineComponent),
    data: {
      defaultSort: `instanceID,${ASC}`,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/view',
    loadComponent: () => import('./detail/address-line-detail.component').then(m => m.AddressLineDetailComponent),
    resolve: {
      addressLine: AddressLineResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    loadComponent: () => import('./update/address-line-update.component').then(m => m.AddressLineUpdateComponent),
    resolve: {
      addressLine: AddressLineResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/edit',
    loadComponent: () => import('./update/address-line-update.component').then(m => m.AddressLineUpdateComponent),
    resolve: {
      addressLine: AddressLineResolve,
    },
    canActivate: [UserRouteAccessService],
  },
];

export default addressLineRoute;
