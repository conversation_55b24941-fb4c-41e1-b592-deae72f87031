import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { of } from 'rxjs';

import { AddressLineDetailComponent } from './address-line-detail.component';

describe('AddressLine Management Detail Component', () => {
  let comp: AddressLineDetailComponent;
  let fixture: ComponentFixture<AddressLineDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddressLineDetailComponent],
      providers: [
        provideRouter(
          [
            {
              path: '**',
              loadComponent: () => import('./address-line-detail.component').then(m => m.AddressLineDetailComponent),
              resolve: { addressLine: () => of({ instanceID: 123 }) },
            },
          ],
          withComponentInputBinding(),
        ),
      ],
    })
      .overrideTemplate(AddressLineDetailComponent, '')
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddressLineDetailComponent);
    comp = fixture.componentInstance;
  });

  describe('OnInit', () => {
    it('Should load addressLine on init', async () => {
      const harness = await RouterTestingHarness.create();
      const instance = await harness.navigateByUrl('/', AddressLineDetailComponent);

      // THEN
      expect(instance.addressLine()).toEqual(expect.objectContaining({ instanceID: 123 }));
    });
  });

  describe('PreviousState', () => {
    it('Should navigate to previous state', () => {
      jest.spyOn(window.history, 'back');
      comp.previousState();
      expect(window.history.back).toHaveBeenCalled();
    });
  });
});
