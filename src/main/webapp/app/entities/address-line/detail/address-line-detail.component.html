<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (addressLine(); as addressLineRef) {
      <div>
        <h2 data-cy="addressLineDetailsHeading">
          <span jhiTranslate="legalReferentialFrontApp.addressLine.detail.title">Address Line</span>
        </h2>

        <hr />

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.instanceID">instanceID</span></dt>
          <dd>
            <span>{{ addressLineRef.instanceID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.addressLineID">Address Line ID</span></dt>
          <dd>
            <span>{{ addressLineRef.addressLineID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.addressSuffix">Address Suffix</span></dt>
          <dd>
            <span>{{ addressLineRef.addressSuffix }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.effectBeginDate">Effect Begin Date</span></dt>
          <dd>
            <span>{{ addressLineRef.effectBeginDate | formatMediumDate }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.effectEndDate">Effect End Date</span></dt>
          <dd>
            <span>{{ addressLineRef.effectEndDate | formatMediumDate }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.effectiveEndDate">Effective End Date</span></dt>
          <dd>
            <span>{{ addressLineRef.effectiveEndDate | formatMediumDate }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.nature">Nature</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.Nature.' + (addressLineRef.nature ?? 'null')">{{
              { null: '', M: 'M', D: 'D' }[addressLineRef.nature ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.creationDate">Creation Date</span></dt>
          <dd>
            <span>{{ addressLineRef.creationDate | formatMediumDate }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.creationBy">Creation By</span></dt>
          <dd>
            <span>{{ addressLineRef.creationBy }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.lastModificationDate">Last Modification Date</span></dt>
          <dd>
            <span>{{ addressLineRef.lastModificationDate | formatMediumDatetime }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.modifiedBy">Modified By</span></dt>
          <dd>
            <span>{{ addressLineRef.modifiedBy }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineRoutingCode">Pk Routing Code</span></dt>
          <dd>
            @if (addressLine()!.fkAddressLineRoutingCode) {
              <div>
                <a [routerLink]="['/routing-code', addressLine()!.fkAddressLineRoutingCode?.instanceID, 'view']">{{
                  addressLineRef.fkAddressLineRoutingCode?.instanceID
                }}</a>
              </div>
            }
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineSiren">Fk Address Line Siren</span></dt>
          <dd>
            @if (addressLine()!.fkAddressLineSiren) {
              <div>
                <a [routerLink]="['/siren', addressLine()!.fkAddressLineSiren?.instanceID, 'view']">{{
                  addressLineRef.fkAddressLineSiren?.instanceID
                }}</a>
              </div>
            }
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineSiret">Fk Address Line Siret</span></dt>
          <dd>
            @if (addressLine()!.fkAddressLineSiret) {
              <div>
                <a [routerLink]="['/siret', addressLine()!.fkAddressLineSiret?.instanceID, 'view']">{{
                  addressLineRef.fkAddressLineSiret?.instanceID
                }}</a>
              </div>
            }
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Retour</span>
        </button>

        <button type="button" [routerLink]="['/address-line', addressLineRef.instanceID, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Editer</span>
        </button>
      </div>
    }
  </div>
</div>
