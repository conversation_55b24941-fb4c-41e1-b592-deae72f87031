import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { DurationPipe, FormatMediumDatePipe, FormatMediumDatetimePipe } from 'app/shared/date';
import { IAddressLine } from '../address-line.model';

@Component({
  standalone: true,
  selector: 'jhi-address-line-detail',
  templateUrl: './address-line-detail.component.html',
  imports: [SharedModule, RouterModule, DurationPipe, FormatMediumDatetimePipe, FormatMediumDatePipe],
})
export class AddressLineDetailComponent {
  addressLine = input<IAddressLine | null>(null);

  previousState(): void {
    window.history.back();
  }
}
