import { Component, OnInit, inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IRoutingCode } from 'app/entities/routing-code/routing-code.model';
import { RoutingCodeService } from 'app/entities/routing-code/service/routing-code.service';
import { ISiren } from 'app/entities/siren/siren.model';
import { SirenService } from 'app/entities/siren/service/siren.service';
import { ISiret } from 'app/entities/siret/siret.model';
import { SiretService } from 'app/entities/siret/service/siret.service';
import { Nature } from 'app/entities/enumerations/nature.model';
import { AddressLineService } from '../service/address-line.service';
import { IAddressLine } from '../address-line.model';
import { AddressLineFormGroup, AddressLineFormService } from './address-line-form.service';

@Component({
  standalone: true,
  selector: 'jhi-address-line-update',
  templateUrl: './address-line-update.component.html',
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class AddressLineUpdateComponent implements OnInit {
  isSaving = false;
  addressLine: IAddressLine | null = null;
  natureValues = Object.keys(Nature);

  fkAddressLineRoutingCodesCollection: IRoutingCode[] = [];
  sirensSharedCollection: ISiren[] = [];
  siretsSharedCollection: ISiret[] = [];

  protected addressLineService = inject(AddressLineService);
  protected addressLineFormService = inject(AddressLineFormService);
  protected routingCodeService = inject(RoutingCodeService);
  protected sirenService = inject(SirenService);
  protected siretService = inject(SiretService);
  protected activatedRoute = inject(ActivatedRoute);

  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: AddressLineFormGroup = this.addressLineFormService.createAddressLineFormGroup();

  compareRoutingCode = (o1: IRoutingCode | null, o2: IRoutingCode | null): boolean => this.routingCodeService.compareRoutingCode(o1, o2);

  compareSiren = (o1: ISiren | null, o2: ISiren | null): boolean => this.sirenService.compareSiren(o1, o2);

  compareSiret = (o1: ISiret | null, o2: ISiret | null): boolean => this.siretService.compareSiret(o1, o2);

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ addressLine }) => {
      this.addressLine = addressLine;
      if (addressLine) {
        this.updateForm(addressLine);
      }

      this.loadRelationshipsOptions();
    });
  }

  previousState(): void {
    window.history.back();
  }

  save(): void {
    this.isSaving = true;
    const addressLine = this.addressLineFormService.getAddressLine(this.editForm);
    if (addressLine.instanceID !== null) {
      this.subscribeToSaveResponse(this.addressLineService.update(addressLine));
    } else {
      this.subscribeToSaveResponse(this.addressLineService.create(addressLine));
    }
  }

  protected subscribeToSaveResponse(result: Observable<HttpResponse<IAddressLine>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSaving = false;
  }

  protected updateForm(addressLine: IAddressLine): void {
    this.addressLine = addressLine;
    this.addressLineFormService.resetForm(this.editForm, addressLine);

    this.fkAddressLineRoutingCodesCollection = this.routingCodeService.addRoutingCodeToCollectionIfMissing<IRoutingCode>(
      this.fkAddressLineRoutingCodesCollection,
      addressLine.fkAddressLineRoutingCode,
    );
    this.sirensSharedCollection = this.sirenService.addSirenToCollectionIfMissing<ISiren>(
      this.sirensSharedCollection,
      addressLine.fkAddressLineSiren,
    );
    this.siretsSharedCollection = this.siretService.addSiretToCollectionIfMissing<ISiret>(
      this.siretsSharedCollection,
      addressLine.fkAddressLineSiret,
    );
  }

  protected loadRelationshipsOptions(): void {
    this.routingCodeService
      .query({ 'fkRoutingCodeAddressLineId.specified': 'false' })
      .pipe(map((res: HttpResponse<IRoutingCode[]>) => res.body ?? []))
      .pipe(
        map((routingCodes: IRoutingCode[]) =>
          this.routingCodeService.addRoutingCodeToCollectionIfMissing<IRoutingCode>(
            routingCodes,
            this.addressLine?.fkAddressLineRoutingCode,
          ),
        ),
      )
      .subscribe((routingCodes: IRoutingCode[]) => (this.fkAddressLineRoutingCodesCollection = routingCodes));

    this.sirenService
      .query()
      .pipe(map((res: HttpResponse<ISiren[]>) => res.body ?? []))
      .pipe(
        map((sirens: ISiren[]) => this.sirenService.addSirenToCollectionIfMissing<ISiren>(sirens, this.addressLine?.fkAddressLineSiren)),
      )
      .subscribe((sirens: ISiren[]) => (this.sirensSharedCollection = sirens));

    this.siretService
      .query()
      .pipe(map((res: HttpResponse<ISiret[]>) => res.body ?? []))
      .pipe(
        map((sirets: ISiret[]) => this.siretService.addSiretToCollectionIfMissing<ISiret>(sirets, this.addressLine?.fkAddressLineSiret)),
      )
      .subscribe((sirets: ISiret[]) => (this.siretsSharedCollection = sirets));
  }
}
