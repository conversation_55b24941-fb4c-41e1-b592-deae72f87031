import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subject, from, of } from 'rxjs';

import { IRoutingCode } from 'app/entities/routing-code/routing-code.model';
import { RoutingCodeService } from 'app/entities/routing-code/service/routing-code.service';
import { ISiren } from 'app/entities/siren/siren.model';
import { SirenService } from 'app/entities/siren/service/siren.service';
import { ISiret } from 'app/entities/siret/siret.model';
import { SiretService } from 'app/entities/siret/service/siret.service';
import { IAddressLine } from '../address-line.model';
import { AddressLineService } from '../service/address-line.service';
import { AddressLineFormService } from './address-line-form.service';

import { AddressLineUpdateComponent } from './address-line-update.component';

describe('AddressLine Management Update Component', () => {
  let comp: AddressLineUpdateComponent;
  let fixture: ComponentFixture<AddressLineUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let addressLineFormService: AddressLineFormService;
  let addressLineService: AddressLineService;
  let routingCodeService: RoutingCodeService;
  let sirenService: SirenService;
  let siretService: SiretService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AddressLineUpdateComponent],
      providers: [
        provideHttpClient(),
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(AddressLineUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(AddressLineUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    addressLineFormService = TestBed.inject(AddressLineFormService);
    addressLineService = TestBed.inject(AddressLineService);
    routingCodeService = TestBed.inject(RoutingCodeService);
    sirenService = TestBed.inject(SirenService);
    siretService = TestBed.inject(SiretService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('Should call fkAddressLineRoutingCode query and add missing value', () => {
      const addressLine: IAddressLine = { instanceID: '456' };
      const fkAddressLineRoutingCode: IRoutingCode = { instanceID: '29550' };
      addressLine.fkAddressLineRoutingCode = fkAddressLineRoutingCode;

      const fkAddressLineRoutingCodeCollection: IRoutingCode[] = [{ instanceID: '31095' }];
      jest.spyOn(routingCodeService, 'query').mockReturnValue(of(new HttpResponse({ body: fkAddressLineRoutingCodeCollection })));
      const expectedCollection: IRoutingCode[] = [fkAddressLineRoutingCode, ...fkAddressLineRoutingCodeCollection];
      jest.spyOn(routingCodeService, 'addRoutingCodeToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ addressLine });
      comp.ngOnInit();

      expect(routingCodeService.query).toHaveBeenCalled();
      expect(routingCodeService.addRoutingCodeToCollectionIfMissing).toHaveBeenCalledWith(
        fkAddressLineRoutingCodeCollection,
        fkAddressLineRoutingCode,
      );
      expect(comp.fkAddressLineRoutingCodesCollection).toEqual(expectedCollection);
    });

    it('Should call Siren query and add missing value', () => {
      const addressLine: IAddressLine = { instanceID: '456' };
      const fkAddressLineSiren: ISiren = { instanceID: '2578' };
      addressLine.fkAddressLineSiren = fkAddressLineSiren;

      const sirenCollection: ISiren[] = [{ instanceID: '3384' }];
      jest.spyOn(sirenService, 'query').mockReturnValue(of(new HttpResponse({ body: sirenCollection })));
      const additionalSirens = [fkAddressLineSiren];
      const expectedCollection: ISiren[] = [...additionalSirens, ...sirenCollection];
      jest.spyOn(sirenService, 'addSirenToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ addressLine });
      comp.ngOnInit();

      expect(sirenService.query).toHaveBeenCalled();
      expect(sirenService.addSirenToCollectionIfMissing).toHaveBeenCalledWith(
        sirenCollection,
        ...additionalSirens.map(expect.objectContaining),
      );
      expect(comp.sirensSharedCollection).toEqual(expectedCollection);
    });

    it('Should call Siret query and add missing value', () => {
      const addressLine: IAddressLine = { instanceID: '456' };
      const fkAddressLineSiret: ISiret = { instanceID: '24646' };
      addressLine.fkAddressLineSiret = fkAddressLineSiret;

      const siretCollection: ISiret[] = [{ instanceID: '10999' }];
      jest.spyOn(siretService, 'query').mockReturnValue(of(new HttpResponse({ body: siretCollection })));
      const additionalSirets = [fkAddressLineSiret];
      const expectedCollection: ISiret[] = [...additionalSirets, ...siretCollection];
      jest.spyOn(siretService, 'addSiretToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ addressLine });
      comp.ngOnInit();

      expect(siretService.query).toHaveBeenCalled();
      expect(siretService.addSiretToCollectionIfMissing).toHaveBeenCalledWith(
        siretCollection,
        ...additionalSirets.map(expect.objectContaining),
      );
      expect(comp.siretsSharedCollection).toEqual(expectedCollection);
    });

    it('Should update editForm', () => {
      const addressLine: IAddressLine = { instanceID: '456' };
      const fkAddressLineRoutingCode: IRoutingCode = { instanceID: '15737' };
      addressLine.fkAddressLineRoutingCode = fkAddressLineRoutingCode;
      const fkAddressLineSiren: ISiren = { instanceID: '7522' };
      addressLine.fkAddressLineSiren = fkAddressLineSiren;
      const fkAddressLineSiret: ISiret = { instanceID: '3115' };
      addressLine.fkAddressLineSiret = fkAddressLineSiret;

      activatedRoute.data = of({ addressLine });
      comp.ngOnInit();

      expect(comp.fkAddressLineRoutingCodesCollection).toContain(fkAddressLineRoutingCode);
      expect(comp.sirensSharedCollection).toContain(fkAddressLineSiren);
      expect(comp.siretsSharedCollection).toContain(fkAddressLineSiret);
      expect(comp.addressLine).toEqual(addressLine);
    });
  });

  describe('save', () => {
    it('Should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAddressLine>>();
      const addressLine = { instanceID: '123' };
      jest.spyOn(addressLineFormService, 'getAddressLine').mockReturnValue(addressLine);
      jest.spyOn(addressLineService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ addressLine });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: addressLine }));
      saveSubject.complete();

      // THEN
      expect(addressLineFormService.getAddressLine).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(addressLineService.update).toHaveBeenCalledWith(expect.objectContaining(addressLine));
      expect(comp.isSaving).toEqual(false);
    });

    it('Should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAddressLine>>();
      const addressLine = { instanceID: '123' };
      jest.spyOn(addressLineFormService, 'getAddressLine').mockReturnValue({ instanceID: null });
      jest.spyOn(addressLineService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ addressLine: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: addressLine }));
      saveSubject.complete();

      // THEN
      expect(addressLineFormService.getAddressLine).toHaveBeenCalled();
      expect(addressLineService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('Should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAddressLine>>();
      const addressLine = { instanceID: 123 };
      jest.spyOn(addressLineService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ addressLine });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(addressLineService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });

  describe('Compare relationships', () => {
    describe('compareRoutingCode', () => {
      it('Should forward to routingCodeService', () => {
        const entity = { instanceID: '123' };
        const entity2 = { instanceID: '456' };
        jest.spyOn(routingCodeService, 'compareRoutingCode');
        comp.compareRoutingCode(entity, entity2);
        expect(routingCodeService.compareRoutingCode).toHaveBeenCalledWith(entity, entity2);
      });
    });

    describe('compareSiren', () => {
      it('Should forward to sirenService', () => {
        const entity = { instanceID: '123' };
        const entity2 = { instanceID: '456' };
        jest.spyOn(sirenService, 'compareSiren');
        comp.compareSiren(entity, entity2);
        expect(sirenService.compareSiren).toHaveBeenCalledWith(entity, entity2);
      });
    });

    describe('compareSiret', () => {
      it('Should forward to siretService', () => {
        const entity = { instanceID: '123' };
        const entity2 = { instanceID: '456' };
        jest.spyOn(siretService, 'compareSiret');
        comp.compareSiret(entity, entity2);
        expect(siretService.compareSiret).toHaveBeenCalledWith(entity, entity2);
      });
    });
  });
});
