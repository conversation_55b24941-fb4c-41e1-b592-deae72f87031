import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import dayjs from 'dayjs/esm';
import { DATE_TIME_FORMAT } from 'app/config/input.constants';
import { IAddressLine, NewAddressLine } from '../address-line.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { instanceID: unknown }> = Partial<Omit<T, 'instanceID'>> & { instanceID: T['instanceID'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IAddressLine for edit and NewAddressLineFormGroupInput for create.
 */
type AddressLineFormGroupInput = IAddressLine | PartialWithRequiredKeyOf<NewAddressLine>;

/**
 * Type that converts some properties for forms.
 */
type FormValueOf<T extends IAddressLine | NewAddressLine> = Omit<T, 'lastModificationDate'> & {
  lastModificationDate?: string | null;
};

type AddressLineFormRawValue = FormValueOf<IAddressLine>;

type NewAddressLineFormRawValue = FormValueOf<NewAddressLine>;

type AddressLineFormDefaults = Pick<NewAddressLine, 'instanceID' | 'lastModificationDate'>;

type AddressLineFormGroupContent = {
  instanceID: FormControl<AddressLineFormRawValue['instanceID'] | NewAddressLine['instanceID']>;
  addressLineID: FormControl<AddressLineFormRawValue['addressLineID']>;
  addressSuffix: FormControl<AddressLineFormRawValue['addressSuffix']>;
  effectBeginDate: FormControl<AddressLineFormRawValue['effectBeginDate']>;
  effectEndDate: FormControl<AddressLineFormRawValue['effectEndDate']>;
  effectiveEndDate: FormControl<AddressLineFormRawValue['effectiveEndDate']>;
  nature: FormControl<AddressLineFormRawValue['nature']>;
  creationDate: FormControl<AddressLineFormRawValue['creationDate']>;
  creationBy: FormControl<AddressLineFormRawValue['creationBy']>;
  lastModificationDate: FormControl<AddressLineFormRawValue['lastModificationDate']>;
  modifiedBy: FormControl<AddressLineFormRawValue['modifiedBy']>;
  fkAddressLineRoutingCode: FormControl<AddressLineFormRawValue['fkAddressLineRoutingCode']>;
  fkAddressLineSiren: FormControl<AddressLineFormRawValue['fkAddressLineSiren']>;
  fkAddressLineSiret: FormControl<AddressLineFormRawValue['fkAddressLineSiret']>;
};

export type AddressLineFormGroup = FormGroup<AddressLineFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class AddressLineFormService {
  createAddressLineFormGroup(addressLine: AddressLineFormGroupInput = { instanceID: null }): AddressLineFormGroup {
    const addressLineRawValue = this.convertAddressLineToAddressLineRawValue({
      ...this.getFormDefaults(),
      ...addressLine,
    });
    return new FormGroup<AddressLineFormGroupContent>({
      instanceID: new FormControl(
        { value: addressLineRawValue.instanceID, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      addressLineID: new FormControl(addressLineRawValue.addressLineID),
      addressSuffix: new FormControl(addressLineRawValue.addressSuffix),
      effectBeginDate: new FormControl(addressLineRawValue.effectBeginDate),
      effectEndDate: new FormControl(addressLineRawValue.effectEndDate),
      effectiveEndDate: new FormControl(addressLineRawValue.effectiveEndDate),
      nature: new FormControl(addressLineRawValue.nature),
      creationDate: new FormControl(addressLineRawValue.creationDate),
      creationBy: new FormControl(addressLineRawValue.creationBy),
      lastModificationDate: new FormControl(addressLineRawValue.lastModificationDate),
      modifiedBy: new FormControl(addressLineRawValue.modifiedBy),
      fkAddressLineRoutingCode: new FormControl(addressLineRawValue.fkAddressLineRoutingCode),
      fkAddressLineSiren: new FormControl(addressLineRawValue.fkAddressLineSiren),
      fkAddressLineSiret: new FormControl(addressLineRawValue.fkAddressLineSiret),
    });
  }

  getAddressLine(form: AddressLineFormGroup): IAddressLine | NewAddressLine {
    return this.convertAddressLineRawValueToAddressLine(form.getRawValue() as AddressLineFormRawValue | NewAddressLineFormRawValue);
  }

  resetForm(form: AddressLineFormGroup, addressLine: AddressLineFormGroupInput): void {
    const addressLineRawValue = this.convertAddressLineToAddressLineRawValue({ ...this.getFormDefaults(), ...addressLine });
    form.reset(
      {
        ...addressLineRawValue,
        instanceID: { value: addressLineRawValue.instanceID, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): AddressLineFormDefaults {
    const currentTime = dayjs();

    return {
      instanceID: null,
      lastModificationDate: currentTime,
    };
  }

  private convertAddressLineRawValueToAddressLine(
    rawAddressLine: AddressLineFormRawValue | NewAddressLineFormRawValue,
  ): IAddressLine | NewAddressLine {
    return {
      ...rawAddressLine,
      lastModificationDate: dayjs(rawAddressLine.lastModificationDate, DATE_TIME_FORMAT),
    };
  }

  private convertAddressLineToAddressLineRawValue(
    addressLine: IAddressLine | (Partial<NewAddressLine> & AddressLineFormDefaults),
  ): AddressLineFormRawValue | PartialWithRequiredKeyOf<NewAddressLineFormRawValue> {
    return {
      ...addressLine,
      lastModificationDate: addressLine.lastModificationDate ? addressLine.lastModificationDate.format(DATE_TIME_FORMAT) : undefined,
    };
  }
}
