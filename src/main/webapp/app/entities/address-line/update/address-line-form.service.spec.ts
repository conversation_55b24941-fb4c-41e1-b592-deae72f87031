import { TestBed } from '@angular/core/testing';

import { sampleWithNewData, sampleWithRequiredData } from '../address-line.test-samples';

import { AddressLineFormService } from './address-line-form.service';

describe('AddressLine Form Service', () => {
  let service: AddressLineFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(AddressLineFormService);
  });

  describe('Service methods', () => {
    describe('createAddressLineFormGroup', () => {
      it('should create a new form with FormControl', () => {
        const formGroup = service.createAddressLineFormGroup();

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            addressLineID: expect.any(Object),
            addressSuffix: expect.any(Object),
            effectBeginDate: expect.any(Object),
            effectEndDate: expect.any(Object),
            effectiveEndDate: expect.any(Object),
            nature: expect.any(Object),
            creationDate: expect.any(Object),
            creationBy: expect.any(Object),
            lastModificationDate: expect.any(Object),
            modifiedBy: expect.any(Object),
            fkAddressLineRoutingCode: expect.any(Object),
            fkAddressLineSiren: expect.any(Object),
            fkAddressLineSiret: expect.any(Object),
          }),
        );
      });

      it('passing IAddressLine should create a new form with FormGroup', () => {
        const formGroup = service.createAddressLineFormGroup(sampleWithRequiredData);

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            addressLineID: expect.any(Object),
            addressSuffix: expect.any(Object),
            effectBeginDate: expect.any(Object),
            effectEndDate: expect.any(Object),
            effectiveEndDate: expect.any(Object),
            nature: expect.any(Object),
            creationDate: expect.any(Object),
            creationBy: expect.any(Object),
            lastModificationDate: expect.any(Object),
            modifiedBy: expect.any(Object),
            fkAddressLineRoutingCode: expect.any(Object),
            fkAddressLineSiren: expect.any(Object),
            fkAddressLineSiret: expect.any(Object),
          }),
        );
      });
    });

    describe('getAddressLine', () => {
      it('should return NewAddressLine for default AddressLine initial value', () => {
        const formGroup = service.createAddressLineFormGroup(sampleWithNewData);

        const addressLine = service.getAddressLine(formGroup) as any;

        expect(addressLine).toMatchObject(sampleWithNewData);
      });

      it('should return NewAddressLine for empty AddressLine initial value', () => {
        const formGroup = service.createAddressLineFormGroup();

        const addressLine = service.getAddressLine(formGroup) as any;

        expect(addressLine).toMatchObject({});
      });

      it('should return IAddressLine', () => {
        const formGroup = service.createAddressLineFormGroup(sampleWithRequiredData);

        const addressLine = service.getAddressLine(formGroup) as any;

        expect(addressLine).toMatchObject(sampleWithRequiredData);
      });
    });

    describe('resetForm', () => {
      it('passing IAddressLine should not enable id FormControl', () => {
        const formGroup = service.createAddressLineFormGroup();
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, sampleWithRequiredData);

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });

      it('passing NewAddressLine should disable id FormControl', () => {
        const formGroup = service.createAddressLineFormGroup(sampleWithRequiredData);
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, { instanceID: null });

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });
    });
  });
});
