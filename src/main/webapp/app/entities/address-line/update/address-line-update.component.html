<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2
        id="jhi-address-line-heading"
        data-cy="AddressLineCreateUpdateHeading"
        jhiTranslate="legalReferentialFrontApp.addressLine.home.createOrEditLabel"
      >
        C<PERSON>er ou éditer un Address Line
      </h2>

      <div>
        <jhi-alert-error></jhi-alert-error>

        @if (editForm.controls.instanceID.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_instanceID" jhiTranslate="legalReferentialFrontApp.addressLine.instanceID"
              >instanceID</label
            >
            <input
              type="text"
              class="form-control"
              name="instanceID"
              id="field_instanceID"
              data-cy="instanceID"
              formControlName="instanceID"
              [readonly]="true"
            />
          </div>
        }

        <div class="mb-3">
          <label class="form-label" for="field_addressLineID" jhiTranslate="legalReferentialFrontApp.addressLine.addressLineID"
            >Address Line ID</label
          >
          <input
            type="text"
            class="form-control"
            name="addressLineID"
            id="field_addressLineID"
            data-cy="addressLineID"
            formControlName="addressLineID"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressSuffix" jhiTranslate="legalReferentialFrontApp.addressLine.addressSuffix"
            >Address Suffix</label
          >
          <input
            type="text"
            class="form-control"
            name="addressSuffix"
            id="field_addressSuffix"
            data-cy="addressSuffix"
            formControlName="addressSuffix"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_effectBeginDate" jhiTranslate="legalReferentialFrontApp.addressLine.effectBeginDate"
            >Effect Begin Date</label
          >
          <div class="input-group">
            <input
              id="field_effectBeginDate"
              data-cy="effectBeginDate"
              type="text"
              class="form-control"
              name="effectBeginDate"
              ngbDatepicker
              #effectBeginDateDp="ngbDatepicker"
              formControlName="effectBeginDate"
            />
            <button type="button" class="btn btn-secondary" (click)="effectBeginDateDp.toggle()">
              <fa-icon icon="calendar-alt"></fa-icon>
            </button>
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_effectEndDate" jhiTranslate="legalReferentialFrontApp.addressLine.effectEndDate"
            >Effect End Date</label
          >
          <div class="input-group">
            <input
              id="field_effectEndDate"
              data-cy="effectEndDate"
              type="text"
              class="form-control"
              name="effectEndDate"
              ngbDatepicker
              #effectEndDateDp="ngbDatepicker"
              formControlName="effectEndDate"
            />
            <button type="button" class="btn btn-secondary" (click)="effectEndDateDp.toggle()">
              <fa-icon icon="calendar-alt"></fa-icon>
            </button>
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_effectiveEndDate" jhiTranslate="legalReferentialFrontApp.addressLine.effectiveEndDate"
            >Effective End Date</label
          >
          <div class="input-group">
            <input
              id="field_effectiveEndDate"
              data-cy="effectiveEndDate"
              type="text"
              class="form-control"
              name="effectiveEndDate"
              ngbDatepicker
              #effectiveEndDateDp="ngbDatepicker"
              formControlName="effectiveEndDate"
            />
            <button type="button" class="btn btn-secondary" (click)="effectiveEndDateDp.toggle()">
              <fa-icon icon="calendar-alt"></fa-icon>
            </button>
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_nature" jhiTranslate="legalReferentialFrontApp.addressLine.nature">Nature</label>
          <select class="form-control" name="nature" formControlName="nature" id="field_nature" data-cy="nature">
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.Nature.null' | translate }}</option>
            @for (nature of natureValues; track $index) {
              <option [value]="nature">{{ 'legalReferentialFrontApp.Nature.' + nature | translate }}</option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_creationDate" jhiTranslate="legalReferentialFrontApp.addressLine.creationDate"
            >Creation Date</label
          >
          <div class="input-group">
            <input
              id="field_creationDate"
              data-cy="creationDate"
              type="text"
              class="form-control"
              name="creationDate"
              ngbDatepicker
              #creationDateDp="ngbDatepicker"
              formControlName="creationDate"
            />
            <button type="button" class="btn btn-secondary" (click)="creationDateDp.toggle()">
              <fa-icon icon="calendar-alt"></fa-icon>
            </button>
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_creationBy" jhiTranslate="legalReferentialFrontApp.addressLine.creationBy"
            >Creation By</label
          >
          <input
            type="text"
            class="form-control"
            name="creationBy"
            id="field_creationBy"
            data-cy="creationBy"
            formControlName="creationBy"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_lastModificationDate"
            jhiTranslate="legalReferentialFrontApp.addressLine.lastModificationDate"
            >Last Modification Date</label
          >
          <div class="d-flex">
            <input
              id="field_lastModificationDate"
              data-cy="lastModificationDate"
              type="datetime-local"
              class="form-control"
              name="lastModificationDate"
              formControlName="lastModificationDate"
              placeholder="YYYY-MM-DD HH:mm"
            />
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_modifiedBy" jhiTranslate="legalReferentialFrontApp.addressLine.modifiedBy"
            >Modified By</label
          >
          <input
            type="text"
            class="form-control"
            name="modifiedBy"
            id="field_modifiedBy"
            data-cy="modifiedBy"
            formControlName="modifiedBy"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_fkAddressLineRoutingCode"
            jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineRoutingCode"
            >Pk Routing Code</label
          >
          <select
            class="form-control"
            id="field_fkAddressLineRoutingCode"
            data-cy="fkAddressLineRoutingCode"
            name="fkAddressLineRoutingCode"
            formControlName="fkAddressLineRoutingCode"
            [compareWith]="compareRoutingCode"
          >
            <option [ngValue]="null"></option>
            @for (routingCodeOption of fkAddressLineRoutingCodesCollection; track $index) {
              <option [ngValue]="routingCodeOption">{{ routingCodeOption.instanceID }}</option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_fkAddressLineSiren" jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineSiren"
            >Fk Address Line Siren</label
          >
          <select
            class="form-control"
            id="field_fkAddressLineSiren"
            data-cy="fkAddressLineSiren"
            name="fkAddressLineSiren"
            formControlName="fkAddressLineSiren"
            [compareWith]="compareSiren"
          >
            <option [ngValue]="null"></option>
            @for (sirenOption of sirensSharedCollection; track $index) {
              <option [ngValue]="sirenOption">{{ sirenOption.instanceID }}</option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_fkAddressLineSiret" jhiTranslate="legalReferentialFrontApp.addressLine.fkAddressLineSiret"
            >Fk Address Line Siret</label
          >
          <select
            class="form-control"
            id="field_fkAddressLineSiret"
            data-cy="fkAddressLineSiret"
            name="fkAddressLineSiret"
            formControlName="fkAddressLineSiret"
            [compareWith]="compareSiret"
          >
            <option [ngValue]="null"></option>
            @for (siretOption of siretsSharedCollection; track $index) {
              <option [ngValue]="siretOption">{{ siretOption.instanceID }}</option>
            }
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Sauvegarder</span>
        </button>
      </div>
    </form>
  </div>
</div>
