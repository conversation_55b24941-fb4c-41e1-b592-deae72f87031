import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';

import { ISiret } from '../siret.model';
import { sampleWithFullData, sampleWithNewData, sampleWithPartialData, sampleWithRequiredData } from '../siret.test-samples';

import { SiretService } from './siret.service';

const requireRestSample: ISiret = {
  ...sampleWithRequiredData,
};

describe('Siret Service', () => {
  let service: SiretService;
  let httpMock: HttpTestingController;
  let expectedResult: ISiret | ISiret[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    expectedResult = null;
    service = TestBed.inject(SiretService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find(123).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a Siret', () => {
      const siret = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(siret).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a Siret', () => {
      const siret = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(siret).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a Siret', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of Siret', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a Siret', () => {
      const expected = true;

      service.delete(123).subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    describe('addSiretToCollectionIfMissing', () => {
      it('should add a Siret to an empty array', () => {
        const siret: ISiret = sampleWithRequiredData;
        expectedResult = service.addSiretToCollectionIfMissing([], siret);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(siret);
      });

      it('should not add a Siret to an array that contains it', () => {
        const siret: ISiret = sampleWithRequiredData;
        const siretCollection: ISiret[] = [
          {
            ...siret,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addSiretToCollectionIfMissing(siretCollection, siret);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a Siret to an array that doesn't contain it", () => {
        const siret: ISiret = sampleWithRequiredData;
        const siretCollection: ISiret[] = [sampleWithPartialData];
        expectedResult = service.addSiretToCollectionIfMissing(siretCollection, siret);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(siret);
      });

      it('should add only unique Siret to an array', () => {
        const siretArray: ISiret[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const siretCollection: ISiret[] = [sampleWithRequiredData];
        expectedResult = service.addSiretToCollectionIfMissing(siretCollection, ...siretArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const siret: ISiret = sampleWithRequiredData;
        const siret2: ISiret = sampleWithPartialData;
        expectedResult = service.addSiretToCollectionIfMissing([], siret, siret2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(siret);
        expect(expectedResult).toContain(siret2);
      });

      it('should accept null and undefined values', () => {
        const siret: ISiret = sampleWithRequiredData;
        expectedResult = service.addSiretToCollectionIfMissing([], null, siret, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(siret);
      });

      it('should return initial array if no Siret is added', () => {
        const siretCollection: ISiret[] = [sampleWithRequiredData];
        expectedResult = service.addSiretToCollectionIfMissing(siretCollection, undefined, null);
        expect(expectedResult).toEqual(siretCollection);
      });
    });

    describe('compareSiret', () => {
      it('Should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareSiret(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('Should return false if one entity is null', () => {
        const entity1 = { instanceID: 123 };
        const entity2 = null;

        const compareResult1 = service.compareSiret(entity1, entity2);
        const compareResult2 = service.compareSiret(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey differs', () => {
        const entity1 = { instanceID: 123 };
        const entity2 = { instanceID: 456 };

        const compareResult1 = service.compareSiret(entity1, entity2);
        const compareResult2 = service.compareSiret(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey matches', () => {
        const entity1 = { instanceID: 123 };
        const entity2 = { instanceID: 123 };

        const compareResult1 = service.compareSiret(entity1, entity2);
        const compareResult2 = service.compareSiret(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
