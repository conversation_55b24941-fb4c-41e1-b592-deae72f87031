import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { ISiret, NewSiret } from '../siret.model';

export type PartialUpdateSiret = Partial<ISiret> & Pick<ISiret, 'instanceID'>;

export type EntityResponseType = HttpResponse<ISiret>;
export type EntityArrayResponseType = HttpResponse<ISiret[]>;

@Injectable({ providedIn: 'root' })
export class SiretService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/sirets');

  create(siret: NewSiret): Observable<EntityResponseType> {
    return this.http.post<ISiret>(this.resourceUrl, siret, { observe: 'response' });
  }

  update(siret: ISiret): Observable<EntityResponseType> {
    return this.http.put<ISiret>(`${this.resourceUrl}/${this.getSiretIdentifier(siret)}`, siret, { observe: 'response' });
  }

  partialUpdate(siret: PartialUpdateSiret): Observable<EntityResponseType> {
    return this.http.patch<ISiret>(`${this.resourceUrl}/${this.getSiretIdentifier(siret)}`, siret, { observe: 'response' });
  }

  find(instanceID: number): Observable<EntityResponseType> {
    return this.http.get<ISiret>(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<ISiret[]>(this.resourceUrl, { params: options, observe: 'response' });
  }

  delete(instanceID: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  getSiretIdentifier(siret: Pick<ISiret, 'instanceID'>): string {
    return siret.instanceID;
  }

  compareSiret(o1: Pick<ISiret, 'instanceID'> | null, o2: Pick<ISiret, 'instanceID'> | null): boolean {
    return o1 && o2 ? this.getSiretIdentifier(o1) === this.getSiretIdentifier(o2) : o1 === o2;
  }

  addSiretToCollectionIfMissing<Type extends Pick<ISiret, 'instanceID'>>(
    siretCollection: Type[],
    ...siretsToCheck: (Type | null | undefined)[]
  ): Type[] {
    const sirets: Type[] = siretsToCheck.filter(isPresent);
    if (sirets.length > 0) {
      const siretCollectionIdentifiers = siretCollection.map(siretItem => this.getSiretIdentifier(siretItem));
      const siretsToAdd = sirets.filter(siretItem => {
        const siretIdentifier = this.getSiretIdentifier(siretItem);
        if (siretCollectionIdentifiers.includes(siretIdentifier)) {
          return false;
        }
        siretCollectionIdentifiers.push(siretIdentifier);
        return true;
      });
      return [...siretsToAdd, ...siretCollection];
    }
    return siretCollection;
  }
}
