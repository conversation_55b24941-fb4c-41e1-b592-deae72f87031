<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (siret(); as siretRef) {
      <div>
        <h2 data-cy="siretDetailsHeading"><span jhiTranslate="legalReferentialFrontApp.siret.detail.title">Siret</span></h2>

        <hr />

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.instanceID">ID</span></dt>
          <dd>
            <span>{{ siretRef.instanceID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.siret">Siret</span></dt>
          <dd>
            <span>{{ siretRef.siret }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.status">Status</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (siretRef.status ?? 'null')">{{
              { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[siretRef.status ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.establishmentType">Establishment Type</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.EstablishmentType.' + (siretRef.establishmentType ?? 'null')">{{
              { null: '', P: 'P', S: 'S' }[siretRef.establishmentType ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.denomination">Denomination</span></dt>
          <dd>
            <span>{{ siretRef.denomination }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressLine1">Address Line 1</span></dt>
          <dd>
            <span>{{ siretRef.addressLine1 }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressLine2">Address Line 2</span></dt>
          <dd>
            <span>{{ siretRef.addressLine2 }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressLine3">Address Line 3</span></dt>
          <dd>
            <span>{{ siretRef.addressLine3 }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressCity">Address City</span></dt>
          <dd>
            <span>{{ siretRef.addressCity }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressCountrySubdivision">Address Country Subdivision</span></dt>
          <dd>
            <span>{{ siretRef.addressCountrySubdivision }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressCountryCode">Address Country Code</span></dt>
          <dd>
            <span>{{ siretRef.addressCountryCode }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressCountryLabel">Address Country Label</span></dt>
          <dd>
            <span>{{ siretRef.addressCountryLabel }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.b2gMoa">B 2 G Moa</span></dt>
          <dd>
            <span>{{ siretRef.b2gMoa }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.b2gMoaOnly">B 2 G Moa Only</span></dt>
          <dd>
            <span>{{ siretRef.b2gMoaOnly }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.b2gPaymentStatusManagement">B 2 G Payment Status Management</span></dt>
          <dd>
            <span>{{ siretRef.b2gPaymentStatusManagement }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.b2gLegalEngagementManagement">B 2 G Legal Engagement Management</span></dt>
          <dd>
            <span>{{ siretRef.b2gLegalEngagementManagement }}</span>
          </dd>
          <dt>
            <span jhiTranslate="legalReferentialFrontApp.siret.b2gLegalOrServiceEngagementManagement"
              >B 2 G Legal Or Service Engagement Management</span
            >
          </dt>
          <dd>
            <span>{{ siretRef.b2gLegalOrServiceEngagementManagement }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.b2gServiceCodeManagement">B 2 G Service Code Management</span></dt>
          <dd>
            <span>{{ siretRef.b2gServiceCodeManagement }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.diffusible">Diffusible</span></dt>
          <dd>
            <span>{{ siretRef.diffusible }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.addressPostalCode">Address Postal Code</span></dt>
          <dd>
            <span>{{ siretRef.addressPostalCode }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siret.fkSiretSiren">Fk Siret Siren</span></dt>
          <dd>
            @if (siret()!.fkSiretSiren) {
              <div>
                <a [routerLink]="['/siren', siret()!.fkSiretSiren?.instanceID, 'view']">{{ siretRef.fkSiretSiren?.instanceID }}</a>
              </div>
            }
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Retour</span>
        </button>

        <button type="button" [routerLink]="['/siret', siretRef.instanceID, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Editer</span>
        </button>
      </div>
    }
  </div>
</div>
