import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { of } from 'rxjs';

import { SiretDetailComponent } from './siret-detail.component';

describe('Siret Management Detail Component', () => {
  let comp: SiretDetailComponent;
  let fixture: ComponentFixture<SiretDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SiretDetailComponent],
      providers: [
        provideRouter(
          [
            {
              path: '**',
              loadComponent: () => import('./siret-detail.component').then(m => m.SiretDetailComponent),
              resolve: { siret: () => of({ instanceID: 123 }) },
            },
          ],
          withComponentInputBinding(),
        ),
      ],
    })
      .overrideTemplate(SiretDetailComponent, '')
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SiretDetailComponent);
    comp = fixture.componentInstance;
  });

  describe('OnInit', () => {
    it('Should load siret on init', async () => {
      const harness = await RouterTestingHarness.create();
      const instance = await harness.navigateByUrl('/', SiretDetailComponent);

      // THEN
      expect(instance.siret()).toEqual(expect.objectContaining({ instanceID: 123 }));
    });
  });

  describe('PreviousState', () => {
    it('Should navigate to previous state', () => {
      jest.spyOn(window.history, 'back');
      comp.previousState();
      expect(window.history.back).toHaveBeenCalled();
    });
  });
});
