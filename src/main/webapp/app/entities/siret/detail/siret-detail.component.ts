import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { DurationPipe, FormatMediumDatePipe, FormatMediumDatetimePipe } from 'app/shared/date';
import { ISiret } from '../siret.model';

@Component({
  standalone: true,
  selector: 'jhi-siret-detail',
  templateUrl: './siret-detail.component.html',
  imports: [SharedModule, RouterModule, DurationPipe, FormatMediumDatetimePipe, FormatMediumDatePipe],
})
export class SiretDetailComponent {
  siret = input<ISiret | null>(null);

  previousState(): void {
    window.history.back();
  }
}
