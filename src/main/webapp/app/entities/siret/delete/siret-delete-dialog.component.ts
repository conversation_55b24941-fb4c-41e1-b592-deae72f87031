import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import SharedModule from 'app/shared/shared.module';
import { ITEM_DELETED_EVENT } from 'app/config/navigation.constants';
import { ISiret } from '../siret.model';
import { SiretService } from '../service/siret.service';

@Component({
  standalone: true,
  templateUrl: './siret-delete-dialog.component.html',
  imports: [SharedModule, FormsModule],
})
export class SiretDeleteDialogComponent {
  siret?: ISiret;

  protected siretService = inject(SiretService);
  protected activeModal = inject(NgbActiveModal);

  cancel(): void {
    this.activeModal.dismiss();
  }

  confirmDelete(instanceID: string): void {
    this.siretService.delete(instanceID).subscribe(() => {
      this.activeModal.close(ITEM_DELETED_EVENT);
    });
  }
}
