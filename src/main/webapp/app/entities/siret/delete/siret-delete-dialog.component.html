@if (siret) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(siret.instanceID!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="siretDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirmation de suppression</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error></jhi-alert-error>
      <p
        id="jhi-delete-siret-heading"
        jhiTranslate="legalReferentialFrontApp.siret.delete.question"
        [translateValues]="{ instanceID: siret.instanceID }"
      >
        Êtes-vous certain de vouloir supprimer le Siret {{ siret.instanceID }} ?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
      </button>

      <button id="jhi-confirm-delete-siret" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Supprimer</span>
      </button>
    </div>
  </form>
}
