import { Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import { ASC } from 'app/config/navigation.constants';
import SiretResolve from './route/siret-routing-resolve.service';

const siretRoute: Routes = [
  {
    path: '',
    loadComponent: () => import('./list/siret.component').then(m => m.SiretComponent),
    data: {
      defaultSort: `instanceID,${ASC}`,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/view',
    loadComponent: () => import('./detail/siret-detail.component').then(m => m.SiretDetailComponent),
    resolve: {
      siret: SiretResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    loadComponent: () => import('./update/siret-update.component').then(m => m.SiretUpdateComponent),
    resolve: {
      siret: SiretResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/edit',
    loadComponent: () => import('./update/siret-update.component').then(m => m.SiretUpdateComponent),
    resolve: {
      siret: SiretResolve,
    },
    canActivate: [UserRouteAccessService],
  },
];

export default siretRoute;
