import { ISiret, NewSiret } from './siret.model';

export const sampleWithRequiredData: ISiret = {
  instanceID: '17047',
};

export const sampleWithPartialData: ISiret = {
  instanceID: '17027',
  establishmentType: 'P',
  addressLine2: 'sous moins',
  addressCity: 'ouin comme',
  addressCountrySubdivision: 'mélancolique',
  addressCountryCode: 'coin-coin étant donné que',
  addressCountryLabel: 'sage à force de',
  b2gLegalOrServiceEngagementManagement: true,
};

export const sampleWithFullData: ISiret = {
  instanceID: '14182',
  siret: 'innombrable',
  status: 'I',
  establishmentType: 'P',
  denomination: 'broum',
  addressLine1: 'délectable police',
  addressLine2: 'juriste main-d’œuvre',
  addressLine3: 'bzzz',
  addressCity: 'dessus délégation à raison de',
  addressCountrySubdivision: 'relier pauvre',
  addressCountryCode: 'au point que',
  addressCountryLabel: 'équipe large',
  b2gMoa: true,
  b2gMoaOnly: false,
  b2gPaymentStatusManagement: true,
  b2gLegalEngagementManagement: true,
  b2gLegalOrServiceEngagementManagement: true,
  b2gServiceCodeManagement: true,
  diffusible: false,
  addressPostalCode: 'de sorte que personnel professionnel blablabla',
};

export const sampleWithNewData: NewSiret = {
  instanceID: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
