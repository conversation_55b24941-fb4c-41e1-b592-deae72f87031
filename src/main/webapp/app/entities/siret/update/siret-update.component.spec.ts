import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subject, from, of } from 'rxjs';

import { ISiren } from 'app/entities/siren/siren.model';
import { SirenService } from 'app/entities/siren/service/siren.service';
import { SiretService } from '../service/siret.service';
import { ISiret } from '../siret.model';
import { SiretFormService } from './siret-form.service';

import { SiretUpdateComponent } from './siret-update.component';

describe('Siret Management Update Component', () => {
  let comp: SiretUpdateComponent;
  let fixture: ComponentFixture<SiretUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let siretFormService: SiretFormService;
  let siretService: SiretService;
  let sirenService: SirenService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [SiretUpdateComponent],
      providers: [
        provideHttpClient(),
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(SiretUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(SiretUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    siretFormService = TestBed.inject(SiretFormService);
    siretService = TestBed.inject(SiretService);
    sirenService = TestBed.inject(SirenService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('Should call Siren query and add missing value', () => {
      const siret: ISiret = { instanceID: 456 };
      const fkSiretSiren: ISiren = { instanceID: 15213 };
      siret.fkSiretSiren = fkSiretSiren;

      const sirenCollection: ISiren[] = [{ instanceID: 14047 }];
      jest.spyOn(sirenService, 'query').mockReturnValue(of(new HttpResponse({ body: sirenCollection })));
      const additionalSirens = [fkSiretSiren];
      const expectedCollection: ISiren[] = [...additionalSirens, ...sirenCollection];
      jest.spyOn(sirenService, 'addSirenToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ siret });
      comp.ngOnInit();

      expect(sirenService.query).toHaveBeenCalled();
      expect(sirenService.addSirenToCollectionIfMissing).toHaveBeenCalledWith(
        sirenCollection,
        ...additionalSirens.map(expect.objectContaining),
      );
      expect(comp.sirensSharedCollection).toEqual(expectedCollection);
    });

    it('Should update editForm', () => {
      const siret: ISiret = { instanceID: 456 };
      const fkSiretSiren: ISiren = { instanceID: 8894 };
      siret.fkSiretSiren = fkSiretSiren;

      activatedRoute.data = of({ siret });
      comp.ngOnInit();

      expect(comp.sirensSharedCollection).toContain(fkSiretSiren);
      expect(comp.siret).toEqual(siret);
    });
  });

  describe('save', () => {
    it('Should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<ISiret>>();
      const siret = { instanceID: 123 };
      jest.spyOn(siretFormService, 'getSiret').mockReturnValue(siret);
      jest.spyOn(siretService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ siret });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: siret }));
      saveSubject.complete();

      // THEN
      expect(siretFormService.getSiret).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(siretService.update).toHaveBeenCalledWith(expect.objectContaining(siret));
      expect(comp.isSaving).toEqual(false);
    });

    it('Should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<ISiret>>();
      const siret = { instanceID: 123 };
      jest.spyOn(siretFormService, 'getSiret').mockReturnValue({ instanceID: null });
      jest.spyOn(siretService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ siret: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: siret }));
      saveSubject.complete();

      // THEN
      expect(siretFormService.getSiret).toHaveBeenCalled();
      expect(siretService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('Should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<ISiret>>();
      const siret = { instanceID: 123 };
      jest.spyOn(siretService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ siret });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(siretService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });

  describe('Compare relationships', () => {
    describe('compareSiren', () => {
      it('Should forward to sirenService', () => {
        const entity = { instanceID: 123 };
        const entity2 = { instanceID: 456 };
        jest.spyOn(sirenService, 'compareSiren');
        comp.compareSiren(entity, entity2);
        expect(sirenService.compareSiren).toHaveBeenCalledWith(entity, entity2);
      });
    });
  });
});
