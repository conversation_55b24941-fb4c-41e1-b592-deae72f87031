import { TestBed } from '@angular/core/testing';

import { sampleWithNewData, sampleWithRequiredData } from '../siret.test-samples';

import { SiretFormService } from './siret-form.service';

describe('Siret Form Service', () => {
  let service: SiretFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SiretFormService);
  });

  describe('Service methods', () => {
    describe('createSiretFormGroup', () => {
      it('should create a new form with FormControl', () => {
        const formGroup = service.createSiretFormGroup();

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            siret: expect.any(Object),
            status: expect.any(Object),
            establishmentType: expect.any(Object),
            denomination: expect.any(Object),
            addressLine1: expect.any(Object),
            addressLine2: expect.any(Object),
            addressLine3: expect.any(Object),
            addressCity: expect.any(Object),
            addressCountrySubdivision: expect.any(Object),
            addressCountryCode: expect.any(Object),
            addressCountryLabel: expect.any(Object),
            b2gMoa: expect.any(Object),
            b2gMoaOnly: expect.any(Object),
            b2gPaymentStatusManagement: expect.any(Object),
            b2gLegalEngagementManagement: expect.any(Object),
            b2gLegalOrServiceEngagementManagement: expect.any(Object),
            b2gServiceCodeManagement: expect.any(Object),
            diffusible: expect.any(Object),
            addressPostalCode: expect.any(Object),
            fkSiretSiren: expect.any(Object),
          }),
        );
      });

      it('passing ISiret should create a new form with FormGroup', () => {
        const formGroup = service.createSiretFormGroup(sampleWithRequiredData);

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            siret: expect.any(Object),
            status: expect.any(Object),
            establishmentType: expect.any(Object),
            denomination: expect.any(Object),
            addressLine1: expect.any(Object),
            addressLine2: expect.any(Object),
            addressLine3: expect.any(Object),
            addressCity: expect.any(Object),
            addressCountrySubdivision: expect.any(Object),
            addressCountryCode: expect.any(Object),
            addressCountryLabel: expect.any(Object),
            b2gMoa: expect.any(Object),
            b2gMoaOnly: expect.any(Object),
            b2gPaymentStatusManagement: expect.any(Object),
            b2gLegalEngagementManagement: expect.any(Object),
            b2gLegalOrServiceEngagementManagement: expect.any(Object),
            b2gServiceCodeManagement: expect.any(Object),
            diffusible: expect.any(Object),
            addressPostalCode: expect.any(Object),
            fkSiretSiren: expect.any(Object),
          }),
        );
      });
    });

    describe('getSiret', () => {
      it('should return NewSiret for default Siret initial value', () => {
        const formGroup = service.createSiretFormGroup(sampleWithNewData);

        const siret = service.getSiret(formGroup) as any;

        expect(siret).toMatchObject(sampleWithNewData);
      });

      it('should return NewSiret for empty Siret initial value', () => {
        const formGroup = service.createSiretFormGroup();

        const siret = service.getSiret(formGroup) as any;

        expect(siret).toMatchObject({});
      });

      it('should return ISiret', () => {
        const formGroup = service.createSiretFormGroup(sampleWithRequiredData);

        const siret = service.getSiret(formGroup) as any;

        expect(siret).toMatchObject(sampleWithRequiredData);
      });
    });

    describe('resetForm', () => {
      it('passing ISiret should not enable id FormControl', () => {
        const formGroup = service.createSiretFormGroup();
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, sampleWithRequiredData);

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });

      it('passing NewSiret should disable id FormControl', () => {
        const formGroup = service.createSiretFormGroup(sampleWithRequiredData);
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, { instanceID: null });

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });
    });
  });
});
