import { Component, OnInit, inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ISiren } from 'app/entities/siren/siren.model';
import { SirenService } from 'app/entities/siren/service/siren.service';
import { Statut } from 'app/entities/enumerations/statut.model';
import { EstablishmentType } from 'app/entities/enumerations/establishment-type.model';
import { SiretService } from '../service/siret.service';
import { ISiret } from '../siret.model';
import { SiretFormGroup, SiretFormService } from './siret-form.service';

@Component({
  standalone: true,
  selector: 'jhi-siret-update',
  templateUrl: './siret-update.component.html',
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class SiretUpdateComponent implements OnInit {
  isSaving = false;
  siret: ISiret | null = null;
  statutValues = Object.keys(Statut);
  establishmentTypeValues = Object.keys(EstablishmentType);

  sirensSharedCollection: ISiren[] = [];

  protected siretService = inject(SiretService);
  protected siretFormService = inject(SiretFormService);
  protected sirenService = inject(SirenService);
  protected activatedRoute = inject(ActivatedRoute);

  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: SiretFormGroup = this.siretFormService.createSiretFormGroup();

  compareSiren = (o1: ISiren | null, o2: ISiren | null): boolean => this.sirenService.compareSiren(o1, o2);

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ siret }) => {
      this.siret = siret;
      if (siret) {
        this.updateForm(siret);
      }

      this.loadRelationshipsOptions();
    });
  }

  previousState(): void {
    window.history.back();
  }

  save(): void {
    this.isSaving = true;
    const siret = this.siretFormService.getSiret(this.editForm);
    if (siret.instanceID !== null) {
      this.subscribeToSaveResponse(this.siretService.update(siret));
    } else {
      this.subscribeToSaveResponse(this.siretService.create(siret));
    }
  }

  protected subscribeToSaveResponse(result: Observable<HttpResponse<ISiret>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSaving = false;
  }

  protected updateForm(siret: ISiret): void {
    this.siret = siret;
    this.siretFormService.resetForm(this.editForm, siret);

    this.sirensSharedCollection = this.sirenService.addSirenToCollectionIfMissing<ISiren>(this.sirensSharedCollection, siret.fkSiretSiren);
  }

  protected loadRelationshipsOptions(): void {
    this.sirenService
      .query()
      .pipe(map((res: HttpResponse<ISiren[]>) => res.body ?? []))
      .pipe(map((sirens: ISiren[]) => this.sirenService.addSirenToCollectionIfMissing<ISiren>(sirens, this.siret?.fkSiretSiren)))
      .subscribe((sirens: ISiren[]) => (this.sirensSharedCollection = sirens));
  }
}
