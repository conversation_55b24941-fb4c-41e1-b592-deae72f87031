import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { ISiret, NewSiret } from '../siret.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { instanceID: unknown }> = Partial<Omit<T, 'id'>> & { instanceID: T['instanceID'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts ISiret for edit and NewSiretFormGroupInput for create.
 */
type SiretFormGroupInput = ISiret | PartialWithRequiredKeyOf<NewSiret>;

type SiretFormDefaults = Pick<
  NewSiret,
  | 'instanceID'
  | 'b2gMoa'
  | 'b2gMoaOnly'
  | 'b2gPaymentStatusManagement'
  | 'b2gLegalEngagementManagement'
  | 'b2gLegalOrServiceEngagementManagement'
  | 'b2gServiceCodeManagement'
  | 'diffusible'
>;

type SiretFormGroupContent = {
  instanceID: FormControl<ISiret['instanceID'] | NewSiret['instanceID']>;
  siret: FormControl<ISiret['siret']>;
  status: FormControl<ISiret['status']>;
  establishmentType: FormControl<ISiret['establishmentType']>;
  denomination: FormControl<ISiret['denomination']>;
  addressLine1: FormControl<ISiret['addressLine1']>;
  addressLine2: FormControl<ISiret['addressLine2']>;
  addressLine3: FormControl<ISiret['addressLine3']>;
  addressCity: FormControl<ISiret['addressCity']>;
  addressCountrySubdivision: FormControl<ISiret['addressCountrySubdivision']>;
  addressCountryCode: FormControl<ISiret['addressCountryCode']>;
  addressCountryLabel: FormControl<ISiret['addressCountryLabel']>;
  b2gMoa: FormControl<ISiret['b2gMoa']>;
  b2gMoaOnly: FormControl<ISiret['b2gMoaOnly']>;
  b2gPaymentStatusManagement: FormControl<ISiret['b2gPaymentStatusManagement']>;
  b2gLegalEngagementManagement: FormControl<ISiret['b2gLegalEngagementManagement']>;
  b2gLegalOrServiceEngagementManagement: FormControl<ISiret['b2gLegalOrServiceEngagementManagement']>;
  b2gServiceCodeManagement: FormControl<ISiret['b2gServiceCodeManagement']>;
  diffusible: FormControl<ISiret['diffusible']>;
  addressPostalCode: FormControl<ISiret['addressPostalCode']>;
  fkSiretSiren: FormControl<ISiret['fkSiretSiren']>;
};

export type SiretFormGroup = FormGroup<SiretFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class SiretFormService {
  createSiretFormGroup(siret: SiretFormGroupInput = { instanceID: null }): SiretFormGroup {
    const siretRawValue = {
      ...this.getFormDefaults(),
      ...siret,
    };
    return new FormGroup<SiretFormGroupContent>({
      instanceID: new FormControl(
        { value: siretRawValue.instanceID, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      siret: new FormControl(siretRawValue.siret),
      status: new FormControl(siretRawValue.status),
      establishmentType: new FormControl(siretRawValue.establishmentType),
      denomination: new FormControl(siretRawValue.denomination),
      addressLine1: new FormControl(siretRawValue.addressLine1),
      addressLine2: new FormControl(siretRawValue.addressLine2),
      addressLine3: new FormControl(siretRawValue.addressLine3),
      addressCity: new FormControl(siretRawValue.addressCity),
      addressCountrySubdivision: new FormControl(siretRawValue.addressCountrySubdivision),
      addressCountryCode: new FormControl(siretRawValue.addressCountryCode),
      addressCountryLabel: new FormControl(siretRawValue.addressCountryLabel),
      b2gMoa: new FormControl(siretRawValue.b2gMoa),
      b2gMoaOnly: new FormControl(siretRawValue.b2gMoaOnly),
      b2gPaymentStatusManagement: new FormControl(siretRawValue.b2gPaymentStatusManagement),
      b2gLegalEngagementManagement: new FormControl(siretRawValue.b2gLegalEngagementManagement),
      b2gLegalOrServiceEngagementManagement: new FormControl(siretRawValue.b2gLegalOrServiceEngagementManagement),
      b2gServiceCodeManagement: new FormControl(siretRawValue.b2gServiceCodeManagement),
      diffusible: new FormControl(siretRawValue.diffusible),
      addressPostalCode: new FormControl(siretRawValue.addressPostalCode),
      fkSiretSiren: new FormControl(siretRawValue.fkSiretSiren),
    });
  }

  getSiret(form: SiretFormGroup): ISiret | NewSiret {
    return form.getRawValue() as ISiret | NewSiret;
  }

  resetForm(form: SiretFormGroup, siret: SiretFormGroupInput): void {
    const siretRawValue = { ...this.getFormDefaults(), ...siret };
    form.reset(
      {
        ...siretRawValue,
        instanceID: { value: siretRawValue.instanceID, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): SiretFormDefaults {
    return {
      instanceID: null,
      b2gMoa: false,
      b2gMoaOnly: false,
      b2gPaymentStatusManagement: false,
      b2gLegalEngagementManagement: false,
      b2gLegalOrServiceEngagementManagement: false,
      b2gServiceCodeManagement: false,
      diffusible: false,
    };
  }
}
