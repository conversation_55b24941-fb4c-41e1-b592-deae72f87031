<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2 id="jhi-siret-heading" data-cy="SiretCreateUpdateHeading" jhiTranslate="legalReferentialFrontApp.siret.home.createOrEditLabel">
        <PERSON><PERSON>er ou éditer un Siret
      </h2>

      <div>
        <jhi-alert-error></jhi-alert-error>

        @if (editForm.controls.instanceID.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_instanceID" jhiTranslate="legalReferentialFrontApp.siret.instanceID">instanceID</label>
            <input
              type="number"
              class="form-control"
              name="instanceID"
              id="field_instanceID"
              data-cy="instanceID"
              formControlName="instanceID"
              [readonly]="true"
            />
          </div>
        }

        <div class="mb-3">
          <label class="form-label" for="field_siret" jhiTranslate="legalReferentialFrontApp.siret.siret">Siret</label>
          <input type="text" class="form-control" name="siret" id="field_siret" data-cy="siret" formControlName="siret" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_status" jhiTranslate="legalReferentialFrontApp.siret.status">Status</label>
          <select class="form-control" name="status" formControlName="status" id="field_status" data-cy="status">
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.Statut.null' | translate }}</option>
            @for (statut of statutValues; track $index) {
              <option [value]="statut">{{ 'legalReferentialFrontApp.Statut.' + statut | translate }}</option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_establishmentType" jhiTranslate="legalReferentialFrontApp.siret.establishmentType"
            >Establishment Type</label
          >
          <select
            class="form-control"
            name="establishmentType"
            formControlName="establishmentType"
            id="field_establishmentType"
            data-cy="establishmentType"
          >
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.EstablishmentType.null' | translate }}</option>
            @for (establishmentType of establishmentTypeValues; track $index) {
              <option [value]="establishmentType">
                {{ 'legalReferentialFrontApp.EstablishmentType.' + establishmentType | translate }}
              </option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_denomination" jhiTranslate="legalReferentialFrontApp.siret.denomination">Denomination</label>
          <input
            type="text"
            class="form-control"
            name="denomination"
            id="field_denomination"
            data-cy="denomination"
            formControlName="denomination"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressLine1" jhiTranslate="legalReferentialFrontApp.siret.addressLine1"
            >Address Line 1</label
          >
          <input
            type="text"
            class="form-control"
            name="addressLine1"
            id="field_addressLine1"
            data-cy="addressLine1"
            formControlName="addressLine1"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressLine2" jhiTranslate="legalReferentialFrontApp.siret.addressLine2"
            >Address Line 2</label
          >
          <input
            type="text"
            class="form-control"
            name="addressLine2"
            id="field_addressLine2"
            data-cy="addressLine2"
            formControlName="addressLine2"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressLine3" jhiTranslate="legalReferentialFrontApp.siret.addressLine3"
            >Address Line 3</label
          >
          <input
            type="text"
            class="form-control"
            name="addressLine3"
            id="field_addressLine3"
            data-cy="addressLine3"
            formControlName="addressLine3"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressCity" jhiTranslate="legalReferentialFrontApp.siret.addressCity">Address City</label>
          <input
            type="text"
            class="form-control"
            name="addressCity"
            id="field_addressCity"
            data-cy="addressCity"
            formControlName="addressCity"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_addressCountrySubdivision"
            jhiTranslate="legalReferentialFrontApp.siret.addressCountrySubdivision"
            >Address Country Subdivision</label
          >
          <input
            type="text"
            class="form-control"
            name="addressCountrySubdivision"
            id="field_addressCountrySubdivision"
            data-cy="addressCountrySubdivision"
            formControlName="addressCountrySubdivision"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressCountryCode" jhiTranslate="legalReferentialFrontApp.siret.addressCountryCode"
            >Address Country Code</label
          >
          <input
            type="text"
            class="form-control"
            name="addressCountryCode"
            id="field_addressCountryCode"
            data-cy="addressCountryCode"
            formControlName="addressCountryCode"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressCountryLabel" jhiTranslate="legalReferentialFrontApp.siret.addressCountryLabel"
            >Address Country Label</label
          >
          <input
            type="text"
            class="form-control"
            name="addressCountryLabel"
            id="field_addressCountryLabel"
            data-cy="addressCountryLabel"
            formControlName="addressCountryLabel"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_b2gMoa" jhiTranslate="legalReferentialFrontApp.siret.b2gMoa">B 2 G Moa</label>
          <input type="checkbox" class="form-check" name="b2gMoa" id="field_b2gMoa" data-cy="b2gMoa" formControlName="b2gMoa" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_b2gMoaOnly" jhiTranslate="legalReferentialFrontApp.siret.b2gMoaOnly">B 2 G Moa Only</label>
          <input
            type="checkbox"
            class="form-check"
            name="b2gMoaOnly"
            id="field_b2gMoaOnly"
            data-cy="b2gMoaOnly"
            formControlName="b2gMoaOnly"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_b2gPaymentStatusManagement"
            jhiTranslate="legalReferentialFrontApp.siret.b2gPaymentStatusManagement"
            >B 2 G Payment Status Management</label
          >
          <input
            type="checkbox"
            class="form-check"
            name="b2gPaymentStatusManagement"
            id="field_b2gPaymentStatusManagement"
            data-cy="b2gPaymentStatusManagement"
            formControlName="b2gPaymentStatusManagement"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_b2gLegalEngagementManagement"
            jhiTranslate="legalReferentialFrontApp.siret.b2gLegalEngagementManagement"
            >B 2 G Legal Engagement Management</label
          >
          <input
            type="checkbox"
            class="form-check"
            name="b2gLegalEngagementManagement"
            id="field_b2gLegalEngagementManagement"
            data-cy="b2gLegalEngagementManagement"
            formControlName="b2gLegalEngagementManagement"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_b2gLegalOrServiceEngagementManagement"
            jhiTranslate="legalReferentialFrontApp.siret.b2gLegalOrServiceEngagementManagement"
            >B 2 G Legal Or Service Engagement Management</label
          >
          <input
            type="checkbox"
            class="form-check"
            name="b2gLegalOrServiceEngagementManagement"
            id="field_b2gLegalOrServiceEngagementManagement"
            data-cy="b2gLegalOrServiceEngagementManagement"
            formControlName="b2gLegalOrServiceEngagementManagement"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_b2gServiceCodeManagement"
            jhiTranslate="legalReferentialFrontApp.siret.b2gServiceCodeManagement"
            >B 2 G Service Code Management</label
          >
          <input
            type="checkbox"
            class="form-check"
            name="b2gServiceCodeManagement"
            id="field_b2gServiceCodeManagement"
            data-cy="b2gServiceCodeManagement"
            formControlName="b2gServiceCodeManagement"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_diffusible" jhiTranslate="legalReferentialFrontApp.siret.diffusible">Diffusible</label>
          <input
            type="checkbox"
            class="form-check"
            name="diffusible"
            id="field_diffusible"
            data-cy="diffusible"
            formControlName="diffusible"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressPostalCode" jhiTranslate="legalReferentialFrontApp.siret.addressPostalCode"
            >Address Postal Code</label
          >
          <input
            type="text"
            class="form-control"
            name="addressPostalCode"
            id="field_addressPostalCode"
            data-cy="addressPostalCode"
            formControlName="addressPostalCode"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_fkSiretSiren" jhiTranslate="legalReferentialFrontApp.siret.fkSiretSiren"
            >Fk Siret Siren</label
          >
          <select
            class="form-control"
            id="field_fkSiretSiren"
            data-cy="fkSiretSiren"
            name="fkSiretSiren"
            formControlName="fkSiretSiren"
            [compareWith]="compareSiren"
          >
            <option [ngValue]="null"></option>
            @for (sirenOption of sirensSharedCollection; track $index) {
              <option [ngValue]="sirenOption">{{ sirenOption.instanceID }}</option>
            }
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Sauvegarder</span>
        </button>
      </div>
    </form>
  </div>
</div>
