<div>
  <h2 id="page-heading" data-cy="SiretHeading">
    <span jhiTranslate="legalReferentialFrontApp.siret.home.title">Sirets</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.siret.home.refreshListLabel">Actualiser la liste</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-siret"
        [routerLink]="['/siret/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.siret.home.createLabel">Créer un nouveau Siret</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error></jhi-alert-error>

  <jhi-alert></jhi-alert>

  <jhi-filter [filters]="filters"></jhi-filter>

  @if (sirets?.length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="legalReferentialFrontApp.siret.home.notFound">Aucun Siret trouvé</span>
    </div>
  }

  @if (sirets && sirets.length > 0) {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [sortState]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="instanceID">
              <div class="d-flex">
                <span jhiTranslate="global.field.instanceID">ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="siret">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.siret">Siret</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="status">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.status">Status</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="establishmentType">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.establishmentType">Establishment Type</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="denomination">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.denomination">Denomination</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressLine1">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressLine1">Address Line 1</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressLine2">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressLine2">Address Line 2</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressLine3">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressLine3">Address Line 3</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCity">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressCity">Address City</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCountrySubdivision">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressCountrySubdivision">Address Country Subdivision</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCountryCode">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressCountryCode">Address Country Code</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCountryLabel">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressCountryLabel">Address Country Label</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="b2gMoa">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.b2gMoa">B 2 G Moa</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="b2gMoaOnly">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.b2gMoaOnly">B 2 G Moa Only</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="b2gPaymentStatusManagement">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.b2gPaymentStatusManagement">B 2 G Payment Status Management</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="b2gLegalEngagementManagement">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.b2gLegalEngagementManagement">B 2 G Legal Engagement Management</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="b2gLegalOrServiceEngagementManagement">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.b2gLegalOrServiceEngagementManagement"
                  >B 2 G Legal Or Service Engagement Management</span
                >

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="b2gServiceCodeManagement">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.b2gServiceCodeManagement">B 2 G Service Code Management</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="diffusible">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.diffusible">Diffusible</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressPostalCode">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.addressPostalCode">Address Postal Code</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="fkSiretSiren.instanceID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siret.fkSiretSiren">Fk Siret Siren</span>
                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (siret of sirets; track trackId(siret)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/siret', siret.instanceID, 'view']">{{ siret.instanceID }}</a>
              </td>
              <td>{{ siret.siret }}</td>
              <td [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (siret.status ?? 'null')">
                {{ { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[siret.status ?? 'null'] }}
              </td>
              <td [jhiTranslate]="'legalReferentialFrontApp.EstablishmentType.' + (siret.establishmentType ?? 'null')">
                {{ { null: '', P: 'P', S: 'S' }[siret.establishmentType ?? 'null'] }}
              </td>
              <td>{{ siret.denomination }}</td>
              <td>{{ siret.addressLine1 }}</td>
              <td>{{ siret.addressLine2 }}</td>
              <td>{{ siret.addressLine3 }}</td>
              <td>{{ siret.addressCity }}</td>
              <td>{{ siret.addressCountrySubdivision }}</td>
              <td>{{ siret.addressCountryCode }}</td>
              <td>{{ siret.addressCountryLabel }}</td>
              <td>{{ siret.b2gMoa }}</td>
              <td>{{ siret.b2gMoaOnly }}</td>
              <td>{{ siret.b2gPaymentStatusManagement }}</td>
              <td>{{ siret.b2gLegalEngagementManagement }}</td>
              <td>{{ siret.b2gLegalOrServiceEngagementManagement }}</td>
              <td>{{ siret.b2gServiceCodeManagement }}</td>
              <td>{{ siret.diffusible }}</td>
              <td>{{ siret.addressPostalCode }}</td>
              <td>
                @if (siret.fkSiretSiren) {
                  <div>
                    <a [routerLink]="['/siren', siret.fkSiretSiren.instanceID, 'view']">{{ siret.fkSiretSiren.instanceID }}</a>
                  </div>
                }
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <button
                    type="submit"
                    [routerLink]="['/routing-code']"
                    [queryParams]="{ 'filter[fkRoutingCodeSiretId.in]': siret.instanceID }"
                    class="btn btn-info btn-sm"
                    data-cy="filterOtherEntityButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span
                      class="d-none d-md-inline"
                      jhiTranslate="entity.action.show"
                      [translateValues]="{ otherEntity: ('legalReferentialFrontApp.siret.pkRoutingCode' | translate) }"
                      >Show Pk Routing Code</span
                    >
                  </button>
                  <button
                    type="submit"
                    [routerLink]="['/address-line']"
                    [queryParams]="{ 'filter[fkAddressLineSiretId.in]': siret.instanceID }"
                    class="btn btn-info btn-sm"
                    data-cy="filterOtherEntityButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span
                      class="d-none d-md-inline"
                      jhiTranslate="entity.action.show"
                      [translateValues]="{ otherEntity: ('legalReferentialFrontApp.siret.pkAddressLine' | translate) }"
                      >Show Pk Address Line</span
                    >
                  </button>
                  <a [routerLink]="['/siret', siret.instanceID, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">Voir</span>
                  </a>

                  <a [routerLink]="['/siret', siret.instanceID, 'edit']" class="btn btn-primary btn-sm" data-cy="entityEditButton">
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Editer</span>
                  </a>

                  <button type="submit" (click)="delete(siret)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Supprimer</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
  @if (sirets && sirets.length > 0) {
    <div>
      <div class="d-flex justify-content-center">
        <jhi-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></jhi-item-count>
      </div>

      <div class="d-flex justify-content-center">
        <ngb-pagination
          [collectionSize]="totalItems"
          [page]="page"
          [pageSize]="itemsPerPage"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          (pageChange)="navigateToPage($event)"
        ></ngb-pagination>
      </div>
    </div>
  }
</div>
