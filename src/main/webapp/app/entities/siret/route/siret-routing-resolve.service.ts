import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { ISiret } from '../siret.model';
import { SiretService } from '../service/siret.service';

const siretResolve = (route: ActivatedRouteSnapshot): Observable<null | ISiret> => {
  const instanceID = route.params.instanceID;
  if (instanceID) {
    return inject(SiretService)
      .find(instanceID)
      .pipe(
        mergeMap((siret: HttpResponse<ISiret>) => {
          if (siret.body) {
            return of(siret.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default siretResolve;
