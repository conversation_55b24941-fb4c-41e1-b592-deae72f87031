import { TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { ActivatedRoute, ActivatedRouteSnapshot, Router, convertToParamMap } from '@angular/router';
import { of } from 'rxjs';

import { ISiret } from '../siret.model';
import { SiretService } from '../service/siret.service';

import siretResolve from './siret-routing-resolve.service';

describe('Siret routing resolve service', () => {
  let mockRouter: Router;
  let mockActivatedRouteSnapshot: ActivatedRouteSnapshot;
  let service: SiretService;
  let resultSiret: ISiret | null | undefined;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: convertToParamMap({}),
            },
          },
        },
      ],
    });
    mockRouter = TestBed.inject(Router);
    jest.spyOn(mockRouter, 'navigate').mockImplementation(() => Promise.resolve(true));
    mockActivatedRouteSnapshot = TestBed.inject(ActivatedRoute).snapshot;
    service = TestBed.inject(SiretService);
    resultSiret = undefined;
  });

  describe('resolve', () => {
    it('should return ISiret returned by find', () => {
      // GIVEN
      service.find = jest.fn(instanceID => of(new HttpResponse({ body: { instanceID } })));
      mockActivatedRouteSnapshot.params = { instanceID: 123 };

      // WHEN
      TestBed.runInInjectionContext(() => {
        siretResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultSiret = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultSiret).toEqual({ instanceID: 123 });
    });

    it('should return null if id is not provided', () => {
      // GIVEN
      service.find = jest.fn();
      mockActivatedRouteSnapshot.params = {};

      // WHEN
      TestBed.runInInjectionContext(() => {
        siretResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultSiret = result;
          },
        });
      });

      // THEN
      expect(service.find).not.toHaveBeenCalled();
      expect(resultSiret).toEqual(null);
    });

    it('should route to 404 page if data not found in server', () => {
      // GIVEN
      jest.spyOn(service, 'find').mockReturnValue(of(new HttpResponse<ISiret>({ body: null })));
      mockActivatedRouteSnapshot.params = { instanceID: 123 };

      // WHEN
      TestBed.runInInjectionContext(() => {
        siretResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultSiret = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultSiret).toEqual(undefined);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['404']);
    });
  });
});
