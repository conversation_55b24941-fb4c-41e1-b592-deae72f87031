import { ISiren } from 'app/entities/siren/siren.model';
import { Statut } from 'app/entities/enumerations/statut.model';
import { EstablishmentType } from 'app/entities/enumerations/establishment-type.model';

export interface ISiret {
  instanceID: string;
  siret?: string | null;
  status?: keyof typeof Statut | null;
  establishmentType?: keyof typeof EstablishmentType | null;
  denomination?: string | null;
  addressLine1?: string | null;
  addressLine2?: string | null;
  addressLine3?: string | null;
  addressCity?: string | null;
  addressCountrySubdivision?: string | null;
  addressCountryCode?: string | null;
  addressCountryLabel?: string | null;
  b2gMoa?: boolean | null;
  b2gMoaOnly?: boolean | null;
  b2gPaymentStatusManagement?: boolean | null;
  b2gLegalEngagementManagement?: boolean | null;
  b2gLegalOrServiceEngagementManagement?: boolean | null;
  b2gServiceCodeManagement?: boolean | null;
  diffusible?: boolean | null;
  addressPostalCode?: string | null;
  fkSiretSiren?: Pick<ISiren, 'instanceID'> | null;
}

export type NewSiret = Omit<ISiret, 'instanceID'> & { instanceID: null };
