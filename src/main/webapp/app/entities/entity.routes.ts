import { Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'siren',
    data: { pageTitle: 'legalReferentialFrontApp.siren.home.title' },
    loadChildren: () => import('./siren/siren.routes'),
  },
  {
    path: 'siret',
    data: { pageTitle: 'legalReferentialFrontApp.siret.home.title' },
    loadChildren: () => import('./siret/siret.routes'),
  },
  {
    path: 'routing-code',
    data: { pageTitle: 'legalReferentialFrontApp.routingCode.home.title' },
    loadChildren: () => import('./routing-code/routing-code.routes'),
  },
  {
    path: 'address-line',
    data: { pageTitle: 'legalReferentialFrontApp.addressLine.home.title' },
    loadChildren: () => import('./address-line/address-line.routes'),
  },
  {
    path: 'platform',
    data: { pageTitle: 'legalReferentialFrontApp.platform.home.title' },
    loadChildren: () => import('./platform/platform.routes'),
  },
  /* jhipster-needle-add-entity-route - JHipster will add entity modules routes here */
];

export default routes;
