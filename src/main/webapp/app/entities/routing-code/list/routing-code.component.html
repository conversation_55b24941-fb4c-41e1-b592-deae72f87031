<div>
  <h2 id="page-heading" data-cy="RoutingCodeHeading">
    <span jhiTranslate="legalReferentialFrontApp.routingCode.home.title">RoutingCodes</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.routingCode.home.refreshListLabel">Actualiser la liste</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-routing-code"
        [routerLink]="['/routing-code/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.routingCode.home.createLabel">Créer un nouveau Routing Code</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error></jhi-alert-error>

  <jhi-alert></jhi-alert>

  <jhi-filter [filters]="filters"></jhi-filter>

  @if (routingCodes?.length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="legalReferentialFrontApp.routingCode.home.notFound">Aucun Routing Code trouvé</span>
    </div>
  }

  @if (routingCodes && routingCodes.length > 0) {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [sortState]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="instanceID">
              <div class="d-flex">
                <span jhiTranslate="global.field.instanceID">ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="routingCodeID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeID">Routing Code ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="status">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.status">Status</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="routingCodeLabel">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeLabel">Routing Code Label</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="routingCodeType">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeType">Routing Code Type</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="administrativeStatus">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.administrativeStatus">Administrative Status</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="legalEngagementManagement">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.legalEngagementManagement">Legal Engagement Management</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressLine1">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressLine1">Address Line 1</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressLine2">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressLine2">Address Line 2</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressLine3">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressLine3">Address Line 3</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressPostalCode">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressPostalCode">Address Postal Code</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCity">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressCity">Address City</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCountrySubdivision">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressCountrySubdivision">Address Country Subdivision</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCountryCode">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressCountryCode">Address Country Code</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="addressCountryLabel">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.addressCountryLabel">Address Country Label</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="fkRoutingCodeSiret.instanceID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.routingCode.fkRoutingCodeSiret">Fk Routing Code Siret</span>
                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (routingCode of routingCodes; track trackId(routingCode)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/routing-code', routingCode.instanceID, 'view']">{{ routingCode.instanceID }}</a>
              </td>
              <td>{{ routingCode.routingCodeID }}</td>
              <td [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (routingCode.status ?? 'null')">
                {{ { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[routingCode.status ?? 'null'] }}
              </td>
              <td>{{ routingCode.routingCodeLabel }}</td>
              <td>{{ routingCode.routingCodeType }}</td>
              <td>{{ routingCode.administrativeStatus }}</td>
              <td>{{ routingCode.legalEngagementManagement }}</td>
              <td>{{ routingCode.addressLine1 }}</td>
              <td>{{ routingCode.addressLine2 }}</td>
              <td>{{ routingCode.addressLine3 }}</td>
              <td>{{ routingCode.addressPostalCode }}</td>
              <td>{{ routingCode.addressCity }}</td>
              <td>{{ routingCode.addressCountrySubdivision }}</td>
              <td>{{ routingCode.addressCountryCode }}</td>
              <td>{{ routingCode.addressCountryLabel }}</td>
              <td>
                @if (routingCode.fkRoutingCodeSiret) {
                  <div>
                    <a [routerLink]="['/siret', routingCode.fkRoutingCodeSiret.instanceID, 'view']">{{
                      routingCode.fkRoutingCodeSiret.instanceID
                    }}</a>
                  </div>
                }
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <a
                    [routerLink]="['/routing-code', routingCode.instanceID, 'view']"
                    class="btn btn-info btn-sm"
                    data-cy="entityDetailsButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">Voir</span>
                  </a>

                  <a
                    [routerLink]="['/routing-code', routingCode.instanceID, 'edit']"
                    class="btn btn-primary btn-sm"
                    data-cy="entityEditButton"
                  >
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Editer</span>
                  </a>

                  <button type="submit" (click)="delete(routingCode)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Supprimer</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
  @if (routingCodes && routingCodes.length > 0) {
    <div>
      <div class="d-flex justify-content-center">
        <jhi-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></jhi-item-count>
      </div>

      <div class="d-flex justify-content-center">
        <ngb-pagination
          [collectionSize]="totalItems"
          [page]="page"
          [pageSize]="itemsPerPage"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          (pageChange)="navigateToPage($event)"
        ></ngb-pagination>
      </div>
    </div>
  }
</div>
