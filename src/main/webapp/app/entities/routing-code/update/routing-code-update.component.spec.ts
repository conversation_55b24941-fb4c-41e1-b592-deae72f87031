import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subject, from, of } from 'rxjs';

import { ISiret } from 'app/entities/siret/siret.model';
import { SiretService } from 'app/entities/siret/service/siret.service';
import { RoutingCodeService } from '../service/routing-code.service';
import { IRoutingCode } from '../routing-code.model';
import { RoutingCodeFormService } from './routing-code-form.service';

import { RoutingCodeUpdateComponent } from './routing-code-update.component';

describe('RoutingCode Management Update Component', () => {
  let comp: RoutingCodeUpdateComponent;
  let fixture: ComponentFixture<RoutingCodeUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let routingCodeFormService: RoutingCodeFormService;
  let routingCodeService: RoutingCodeService;
  let siretService: SiretService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [RoutingCodeUpdateComponent],
      providers: [
        provideHttpClient(),
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(RoutingCodeUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(RoutingCodeUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    routingCodeFormService = TestBed.inject(RoutingCodeFormService);
    routingCodeService = TestBed.inject(RoutingCodeService);
    siretService = TestBed.inject(SiretService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('Should call Siret query and add missing value', () => {
      const routingCode: IRoutingCode = { instanceID: 456 };
      const fkRoutingCodeSiret: ISiret = { instanceID: 28937 };
      routingCode.fkRoutingCodeSiret = fkRoutingCodeSiret;

      const siretCollection: ISiret[] = [{ instanceID: 19031 }];
      jest.spyOn(siretService, 'query').mockReturnValue(of(new HttpResponse({ body: siretCollection })));
      const additionalSirets = [fkRoutingCodeSiret];
      const expectedCollection: ISiret[] = [...additionalSirets, ...siretCollection];
      jest.spyOn(siretService, 'addSiretToCollectionIfMissing').mockReturnValue(expectedCollection);

      activatedRoute.data = of({ routingCode });
      comp.ngOnInit();

      expect(siretService.query).toHaveBeenCalled();
      expect(siretService.addSiretToCollectionIfMissing).toHaveBeenCalledWith(
        siretCollection,
        ...additionalSirets.map(expect.objectContaining),
      );
      expect(comp.siretsSharedCollection).toEqual(expectedCollection);
    });

    it('Should update editForm', () => {
      const routingCode: IRoutingCode = { instanceID: 456 };
      const fkRoutingCodeSiret: ISiret = { instanceID: 2464 };
      routingCode.fkRoutingCodeSiret = fkRoutingCodeSiret;

      activatedRoute.data = of({ routingCode });
      comp.ngOnInit();

      expect(comp.siretsSharedCollection).toContain(fkRoutingCodeSiret);
      expect(comp.routingCode).toEqual(routingCode);
    });
  });

  describe('save', () => {
    it('Should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IRoutingCode>>();
      const routingCode = { instanceID: 123 };
      jest.spyOn(routingCodeFormService, 'getRoutingCode').mockReturnValue(routingCode);
      jest.spyOn(routingCodeService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ routingCode });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: routingCode }));
      saveSubject.complete();

      // THEN
      expect(routingCodeFormService.getRoutingCode).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(routingCodeService.update).toHaveBeenCalledWith(expect.objectContaining(routingCode));
      expect(comp.isSaving).toEqual(false);
    });

    it('Should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IRoutingCode>>();
      const routingCode = { instanceID: 123 };
      jest.spyOn(routingCodeFormService, 'getRoutingCode').mockReturnValue({ instanceID: null });
      jest.spyOn(routingCodeService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ routingCode: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: routingCode }));
      saveSubject.complete();

      // THEN
      expect(routingCodeFormService.getRoutingCode).toHaveBeenCalled();
      expect(routingCodeService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('Should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IRoutingCode>>();
      const routingCode = { instanceID: 123 };
      jest.spyOn(routingCodeService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ routingCode });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(routingCodeService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });

  describe('Compare relationships', () => {
    describe('compareSiret', () => {
      it('Should forward to siretService', () => {
        const entity = { instanceID: 123 };
        const entity2 = { instanceID: 456 };
        jest.spyOn(siretService, 'compareSiret');
        comp.compareSiret(entity, entity2);
        expect(siretService.compareSiret).toHaveBeenCalledWith(entity, entity2);
      });
    });
  });
});
