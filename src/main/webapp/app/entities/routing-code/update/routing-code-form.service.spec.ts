import { TestBed } from '@angular/core/testing';

import { sampleWithNewData, sampleWithRequiredData } from '../routing-code.test-samples';

import { RoutingCodeFormService } from './routing-code-form.service';

describe('RoutingCode Form Service', () => {
  let service: RoutingCodeFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(RoutingCodeFormService);
  });

  describe('Service methods', () => {
    describe('createRoutingCodeFormGroup', () => {
      it('should create a new form with FormControl', () => {
        const formGroup = service.createRoutingCodeFormGroup();

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            routingCodeID: expect.any(Object),
            status: expect.any(Object),
            routingCodeLabel: expect.any(Object),
            routingCodeType: expect.any(Object),
            administrativeStatus: expect.any(Object),
            legalEngagementManagement: expect.any(Object),
            addressLine1: expect.any(Object),
            addressLine2: expect.any(Object),
            addressLine3: expect.any(Object),
            addressPostalCode: expect.any(Object),
            addressCity: expect.any(Object),
            addressCountrySubdivision: expect.any(Object),
            addressCountryCode: expect.any(Object),
            addressCountryLabel: expect.any(Object),
            fkRoutingCodeSiret: expect.any(Object),
          }),
        );
      });

      it('passing IRoutingCode should create a new form with FormGroup', () => {
        const formGroup = service.createRoutingCodeFormGroup(sampleWithRequiredData);

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            routingCodeID: expect.any(Object),
            status: expect.any(Object),
            routingCodeLabel: expect.any(Object),
            routingCodeType: expect.any(Object),
            administrativeStatus: expect.any(Object),
            legalEngagementManagement: expect.any(Object),
            addressLine1: expect.any(Object),
            addressLine2: expect.any(Object),
            addressLine3: expect.any(Object),
            addressPostalCode: expect.any(Object),
            addressCity: expect.any(Object),
            addressCountrySubdivision: expect.any(Object),
            addressCountryCode: expect.any(Object),
            addressCountryLabel: expect.any(Object),
            fkRoutingCodeSiret: expect.any(Object),
          }),
        );
      });
    });

    describe('getRoutingCode', () => {
      it('should return NewRoutingCode for default RoutingCode initial value', () => {
        const formGroup = service.createRoutingCodeFormGroup(sampleWithNewData);

        const routingCode = service.getRoutingCode(formGroup) as any;

        expect(routingCode).toMatchObject(sampleWithNewData);
      });

      it('should return NewRoutingCode for empty RoutingCode initial value', () => {
        const formGroup = service.createRoutingCodeFormGroup();

        const routingCode = service.getRoutingCode(formGroup) as any;

        expect(routingCode).toMatchObject({});
      });

      it('should return IRoutingCode', () => {
        const formGroup = service.createRoutingCodeFormGroup(sampleWithRequiredData);

        const routingCode = service.getRoutingCode(formGroup) as any;

        expect(routingCode).toMatchObject(sampleWithRequiredData);
      });
    });

    describe('resetForm', () => {
      it('passing IRoutingCode should not enable id FormControl', () => {
        const formGroup = service.createRoutingCodeFormGroup();
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, sampleWithRequiredData);

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });

      it('passing NewRoutingCode should disable id FormControl', () => {
        const formGroup = service.createRoutingCodeFormGroup(sampleWithRequiredData);
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, { instanceID: null });

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });
    });
  });
});
