<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2
        id="jhi-routing-code-heading"
        data-cy="RoutingCodeCreateUpdateHeading"
        jhiTranslate="legalReferentialFrontApp.routingCode.home.createOrEditLabel"
      >
        Créer ou éditer un Routing Code
      </h2>

      <div>
        <jhi-alert-error></jhi-alert-error>

        @if (editForm.controls.instanceID.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_instanceID" jhiTranslate="legalReferentialFrontApp.routingCode.instanceID"
              >instanceID</label
            >
            <input
              type="text"
              class="form-control"
              name="instanceID"
              id="field_instanceID"
              data-cy="instanceID"
              formControlName="instanceID"
              [readonly]="true"
            />
          </div>
        }

        <div class="mb-3">
          <label class="form-label" for="field_routingCodeID" jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeID"
            >Routing Code ID</label
          >
          <input
            type="text"
            class="form-control"
            name="routingCodeID"
            id="field_routingCodeID"
            data-cy="routingCodeID"
            formControlName="routingCodeID"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_status" jhiTranslate="legalReferentialFrontApp.routingCode.status">Status</label>
          <select class="form-control" name="status" formControlName="status" id="field_status" data-cy="status">
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.Statut.null' | translate }}</option>
            @for (statut of statutValues; track $index) {
              <option [value]="statut">{{ 'legalReferentialFrontApp.Statut.' + statut | translate }}</option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_routingCodeLabel" jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeLabel"
            >Routing Code Label</label
          >
          <input
            type="text"
            class="form-control"
            name="routingCodeLabel"
            id="field_routingCodeLabel"
            data-cy="routingCodeLabel"
            formControlName="routingCodeLabel"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_routingCodeType" jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeType"
            >Routing Code Type</label
          >
          <input
            type="text"
            class="form-control"
            name="routingCodeType"
            id="field_routingCodeType"
            data-cy="routingCodeType"
            formControlName="routingCodeType"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_administrativeStatus"
            jhiTranslate="legalReferentialFrontApp.routingCode.administrativeStatus"
            >Administrative Status</label
          >
          <input
            type="text"
            class="form-control"
            name="administrativeStatus"
            id="field_administrativeStatus"
            data-cy="administrativeStatus"
            formControlName="administrativeStatus"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_legalEngagementManagement"
            jhiTranslate="legalReferentialFrontApp.routingCode.legalEngagementManagement"
            >Legal Engagement Management</label
          >
          <input
            type="checkbox"
            class="form-check"
            name="legalEngagementManagement"
            id="field_legalEngagementManagement"
            data-cy="legalEngagementManagement"
            formControlName="legalEngagementManagement"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressLine1" jhiTranslate="legalReferentialFrontApp.routingCode.addressLine1"
            >Address Line 1</label
          >
          <input
            type="text"
            class="form-control"
            name="addressLine1"
            id="field_addressLine1"
            data-cy="addressLine1"
            formControlName="addressLine1"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressLine2" jhiTranslate="legalReferentialFrontApp.routingCode.addressLine2"
            >Address Line 2</label
          >
          <input
            type="text"
            class="form-control"
            name="addressLine2"
            id="field_addressLine2"
            data-cy="addressLine2"
            formControlName="addressLine2"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressLine3" jhiTranslate="legalReferentialFrontApp.routingCode.addressLine3"
            >Address Line 3</label
          >
          <input
            type="text"
            class="form-control"
            name="addressLine3"
            id="field_addressLine3"
            data-cy="addressLine3"
            formControlName="addressLine3"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressPostalCode" jhiTranslate="legalReferentialFrontApp.routingCode.addressPostalCode"
            >Address Postal Code</label
          >
          <input
            type="text"
            class="form-control"
            name="addressPostalCode"
            id="field_addressPostalCode"
            data-cy="addressPostalCode"
            formControlName="addressPostalCode"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressCity" jhiTranslate="legalReferentialFrontApp.routingCode.addressCity"
            >Address City</label
          >
          <input
            type="text"
            class="form-control"
            name="addressCity"
            id="field_addressCity"
            data-cy="addressCity"
            formControlName="addressCity"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_addressCountrySubdivision"
            jhiTranslate="legalReferentialFrontApp.routingCode.addressCountrySubdivision"
            >Address Country Subdivision</label
          >
          <input
            type="text"
            class="form-control"
            name="addressCountrySubdivision"
            id="field_addressCountrySubdivision"
            data-cy="addressCountrySubdivision"
            formControlName="addressCountrySubdivision"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressCountryCode" jhiTranslate="legalReferentialFrontApp.routingCode.addressCountryCode"
            >Address Country Code</label
          >
          <input
            type="text"
            class="form-control"
            name="addressCountryCode"
            id="field_addressCountryCode"
            data-cy="addressCountryCode"
            formControlName="addressCountryCode"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_addressCountryLabel" jhiTranslate="legalReferentialFrontApp.routingCode.addressCountryLabel"
            >Address Country Label</label
          >
          <input
            type="text"
            class="form-control"
            name="addressCountryLabel"
            id="field_addressCountryLabel"
            data-cy="addressCountryLabel"
            formControlName="addressCountryLabel"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_fkRoutingCodeSiret" jhiTranslate="legalReferentialFrontApp.routingCode.fkRoutingCodeSiret"
            >Fk Routing Code Siret</label
          >
          <select
            class="form-control"
            id="field_fkRoutingCodeSiret"
            data-cy="fkRoutingCodeSiret"
            name="fkRoutingCodeSiret"
            formControlName="fkRoutingCodeSiret"
            [compareWith]="compareSiret"
          >
            <option [ngValue]="null"></option>
            @for (siretOption of siretsSharedCollection; track $index) {
              <option [ngValue]="siretOption">{{ siretOption.instanceID }}</option>
            }
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Sauvegarder</span>
        </button>
      </div>
    </form>
  </div>
</div>
