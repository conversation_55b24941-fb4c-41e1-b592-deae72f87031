import { Component, OnInit, inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ISiret } from 'app/entities/siret/siret.model';
import { SiretService } from 'app/entities/siret/service/siret.service';
import { Statut } from 'app/entities/enumerations/statut.model';
import { RoutingCodeService } from '../service/routing-code.service';
import { IRoutingCode } from '../routing-code.model';
import { RoutingCodeFormGroup, RoutingCodeFormService } from './routing-code-form.service';

@Component({
  standalone: true,
  selector: 'jhi-routing-code-update',
  templateUrl: './routing-code-update.component.html',
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class RoutingCodeUpdateComponent implements OnInit {
  isSaving = false;
  routingCode: IRoutingCode | null = null;
  statutValues = Object.keys(Statut);

  siretsSharedCollection: ISiret[] = [];

  protected routingCodeService = inject(RoutingCodeService);
  protected routingCodeFormService = inject(RoutingCodeFormService);
  protected siretService = inject(SiretService);
  protected activatedRoute = inject(ActivatedRoute);

  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: RoutingCodeFormGroup = this.routingCodeFormService.createRoutingCodeFormGroup();

  compareSiret = (o1: ISiret | null, o2: ISiret | null): boolean => this.siretService.compareSiret(o1, o2);

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ routingCode }) => {
      this.routingCode = routingCode;
      if (routingCode) {
        this.updateForm(routingCode);
      }

      this.loadRelationshipsOptions();
    });
  }

  previousState(): void {
    window.history.back();
  }

  save(): void {
    this.isSaving = true;
    const routingCode = this.routingCodeFormService.getRoutingCode(this.editForm);
    if (routingCode.instanceID !== null) {
      this.subscribeToSaveResponse(this.routingCodeService.update(routingCode));
    } else {
      this.subscribeToSaveResponse(this.routingCodeService.create(routingCode));
    }
  }

  protected subscribeToSaveResponse(result: Observable<HttpResponse<IRoutingCode>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSaving = false;
  }

  protected updateForm(routingCode: IRoutingCode): void {
    this.routingCode = routingCode;
    this.routingCodeFormService.resetForm(this.editForm, routingCode);

    this.siretsSharedCollection = this.siretService.addSiretToCollectionIfMissing<ISiret>(
      this.siretsSharedCollection,
      routingCode.fkRoutingCodeSiret,
    );
  }

  protected loadRelationshipsOptions(): void {
    this.siretService
      .query()
      .pipe(map((res: HttpResponse<ISiret[]>) => res.body ?? []))
      .pipe(
        map((sirets: ISiret[]) => this.siretService.addSiretToCollectionIfMissing<ISiret>(sirets, this.routingCode?.fkRoutingCodeSiret)),
      )
      .subscribe((sirets: ISiret[]) => (this.siretsSharedCollection = sirets));
  }
}
