import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { IRoutingCode, NewRoutingCode } from '../routing-code.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { instanceID: unknown }> = Partial<Omit<T, 'instanceID'>> & { instanceID: T['instanceID'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IRoutingCode for edit and NewRoutingCodeFormGroupInput for create.
 */
type RoutingCodeFormGroupInput = IRoutingCode | PartialWithRequiredKeyOf<NewRoutingCode>;

type RoutingCodeFormDefaults = Pick<NewRoutingCode, 'instanceID' | 'legalEngagementManagement'>;

type RoutingCodeFormGroupContent = {
  instanceID: FormControl<IRoutingCode['instanceID'] | NewRoutingCode['instanceID']>;
  routingCodeID: FormControl<IRoutingCode['routingCodeID']>;
  status: FormControl<IRoutingCode['status']>;
  routingCodeLabel: FormControl<IRoutingCode['routingCodeLabel']>;
  routingCodeType: FormControl<IRoutingCode['routingCodeType']>;
  administrativeStatus: FormControl<IRoutingCode['administrativeStatus']>;
  legalEngagementManagement: FormControl<IRoutingCode['legalEngagementManagement']>;
  addressLine1: FormControl<IRoutingCode['addressLine1']>;
  addressLine2: FormControl<IRoutingCode['addressLine2']>;
  addressLine3: FormControl<IRoutingCode['addressLine3']>;
  addressPostalCode: FormControl<IRoutingCode['addressPostalCode']>;
  addressCity: FormControl<IRoutingCode['addressCity']>;
  addressCountrySubdivision: FormControl<IRoutingCode['addressCountrySubdivision']>;
  addressCountryCode: FormControl<IRoutingCode['addressCountryCode']>;
  addressCountryLabel: FormControl<IRoutingCode['addressCountryLabel']>;
  fkRoutingCodeSiret: FormControl<IRoutingCode['fkRoutingCodeSiret']>;
};

export type RoutingCodeFormGroup = FormGroup<RoutingCodeFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class RoutingCodeFormService {
  createRoutingCodeFormGroup(routingCode: RoutingCodeFormGroupInput = { instanceID: null }): RoutingCodeFormGroup {
    const routingCodeRawValue = {
      ...this.getFormDefaults(),
      ...routingCode,
    };
    return new FormGroup<RoutingCodeFormGroupContent>({
      instanceID: new FormControl(
        { value: routingCodeRawValue.instanceID, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      routingCodeID: new FormControl(routingCodeRawValue.routingCodeID),
      status: new FormControl(routingCodeRawValue.status),
      routingCodeLabel: new FormControl(routingCodeRawValue.routingCodeLabel),
      routingCodeType: new FormControl(routingCodeRawValue.routingCodeType),
      administrativeStatus: new FormControl(routingCodeRawValue.administrativeStatus),
      legalEngagementManagement: new FormControl(routingCodeRawValue.legalEngagementManagement),
      addressLine1: new FormControl(routingCodeRawValue.addressLine1),
      addressLine2: new FormControl(routingCodeRawValue.addressLine2),
      addressLine3: new FormControl(routingCodeRawValue.addressLine3),
      addressPostalCode: new FormControl(routingCodeRawValue.addressPostalCode),
      addressCity: new FormControl(routingCodeRawValue.addressCity),
      addressCountrySubdivision: new FormControl(routingCodeRawValue.addressCountrySubdivision),
      addressCountryCode: new FormControl(routingCodeRawValue.addressCountryCode),
      addressCountryLabel: new FormControl(routingCodeRawValue.addressCountryLabel),
      fkRoutingCodeSiret: new FormControl(routingCodeRawValue.fkRoutingCodeSiret),
    });
  }

  getRoutingCode(form: RoutingCodeFormGroup): IRoutingCode | NewRoutingCode {
    return form.getRawValue() as IRoutingCode | NewRoutingCode;
  }

  resetForm(form: RoutingCodeFormGroup, routingCode: RoutingCodeFormGroupInput): void {
    const routingCodeRawValue = { ...this.getFormDefaults(), ...routingCode };
    form.reset(
      {
        ...routingCodeRawValue,
        instanceID: { value: routingCodeRawValue.instanceID, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): RoutingCodeFormDefaults {
    return {
      instanceID: null,
      legalEngagementManagement: false,
    };
  }
}
