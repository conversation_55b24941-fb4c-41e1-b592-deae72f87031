import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IRoutingCode } from '../routing-code.model';
import { RoutingCodeService } from '../service/routing-code.service';

const routingCodeResolve = (route: ActivatedRouteSnapshot): Observable<null | IRoutingCode> => {
  const instanceID = route.params.instanceID;
  if (instanceID) {
    return inject(RoutingCodeService)
      .find(instanceID)
      .pipe(
        mergeMap((routingCode: HttpResponse<IRoutingCode>) => {
          if (routingCode.body) {
            return of(routingCode.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default routingCodeResolve;
