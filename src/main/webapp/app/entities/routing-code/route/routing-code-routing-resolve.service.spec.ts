import { TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { ActivatedRoute, ActivatedRouteSnapshot, Router, convertToParamMap } from '@angular/router';
import { of } from 'rxjs';

import { IRoutingCode } from '../routing-code.model';
import { RoutingCodeService } from '../service/routing-code.service';

import routingCodeResolve from './routing-code-routing-resolve.service';

describe('RoutingCode routing resolve service', () => {
  let mockRouter: Router;
  let mockActivatedRouteSnapshot: ActivatedRouteSnapshot;
  let service: RoutingCodeService;
  let resultRoutingCode: IRoutingCode | null | undefined;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: convertToParamMap({}),
            },
          },
        },
      ],
    });
    mockRouter = TestBed.inject(Router);
    jest.spyOn(mockRouter, 'navigate').mockImplementation(() => Promise.resolve(true));
    mockActivatedRouteSnapshot = TestBed.inject(ActivatedRoute).snapshot;
    service = TestBed.inject(RoutingCodeService);
    resultRoutingCode = undefined;
  });

  describe('resolve', () => {
    it('should return IRoutingCode returned by find', () => {
      // GIVEN
      service.find = jest.fn(instanceID => of(new HttpResponse({ body: { instanceID } })));
      mockActivatedRouteSnapshot.params = { instanceID: '123' };

      // WHEN
      TestBed.runInInjectionContext(() => {
        routingCodeResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultRoutingCode = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultRoutingCode).toEqual({ instanceID: 123 });
    });

    it('should return null if id is not provided', () => {
      // GIVEN
      service.find = jest.fn();
      mockActivatedRouteSnapshot.params = {};

      // WHEN
      TestBed.runInInjectionContext(() => {
        routingCodeResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultRoutingCode = result;
          },
        });
      });

      // THEN
      expect(service.find).not.toHaveBeenCalled();
      expect(resultRoutingCode).toEqual(null);
    });

    it('should route to 404 page if data not found in server', () => {
      // GIVEN
      jest.spyOn(service, 'find').mockReturnValue(of(new HttpResponse<IRoutingCode>({ body: null })));
      mockActivatedRouteSnapshot.params = { instanceID: 123 };

      // WHEN
      TestBed.runInInjectionContext(() => {
        routingCodeResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultRoutingCode = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultRoutingCode).toEqual(undefined);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['404']);
    });
  });
});
