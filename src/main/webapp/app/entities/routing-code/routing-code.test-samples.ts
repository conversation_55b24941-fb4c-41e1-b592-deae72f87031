import { IRoutingCode, NewRoutingCode } from './routing-code.model';

export const sampleWithRequiredData: IRoutingCode = {
  instanceID: 11968,
};

export const sampleWithPartialData: IRoutingCode = {
  instanceID: 13997,
  routingCodeLabel: 'brusque étouffer',
  routingCodeType: 'miam groin groin',
  legalEngagementManagement: true,
  addressLine2: 'si bien que ouin',
  addressLine3: 'gratis',
  addressCountrySubdivision: 'croâ ouille dedans',
  addressCountryLabel: 'bouger oui',
};

export const sampleWithFullData: IRoutingCode = {
  instanceID: 24404,
  routingCodeID: 'souligner',
  status: 'C',
  routingCodeLabel: 'quand redevenir',
  routingCodeType: 'auparavant toutefois',
  administrativeStatus: 'secouriste',
  legalEngagementManagement: false,
  addressLine1: 'conseil d’administration à force de assez',
  addressLine2: 'sédentaire moderne',
  addressLine3: 'bè',
  addressPostalCode: 'en plus de autour de chut',
  addressCity: 'équipe de recherche choisir',
  addressCountrySubdivision: 'tsoin-tsoin pendant que',
  addressCountryCode: 'avant dérober aïe',
  addressCountryLabel: 'causer admirablement athlète',
};

export const sampleWithNewData: NewRoutingCode = {
  instanceID: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
