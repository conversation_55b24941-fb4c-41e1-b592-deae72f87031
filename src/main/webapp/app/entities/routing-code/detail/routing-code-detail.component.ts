import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { DurationPipe, FormatMediumDatePipe, FormatMediumDatetimePipe } from 'app/shared/date';
import { IRoutingCode } from '../routing-code.model';

@Component({
  standalone: true,
  selector: 'jhi-routing-code-detail',
  templateUrl: './routing-code-detail.component.html',
  imports: [SharedModule, RouterModule, DurationPipe, FormatMediumDatetimePipe, FormatMediumDatePipe],
})
export class RoutingCodeDetailComponent {
  routingCode = input<IRoutingCode | null>(null);

  previousState(): void {
    window.history.back();
  }
}
