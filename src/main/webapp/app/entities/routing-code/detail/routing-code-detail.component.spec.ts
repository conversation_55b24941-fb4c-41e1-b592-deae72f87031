import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { of } from 'rxjs';

import { RoutingCodeDetailComponent } from './routing-code-detail.component';

describe('RoutingCode Management Detail Component', () => {
  let comp: RoutingCodeDetailComponent;
  let fixture: ComponentFixture<RoutingCodeDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RoutingCodeDetailComponent],
      providers: [
        provideRouter(
          [
            {
              path: '**',
              loadComponent: () => import('./routing-code-detail.component').then(m => m.RoutingCodeDetailComponent),
              resolve: { routingCode: () => of({ instanceID: 123 }) },
            },
          ],
          withComponentInputBinding(),
        ),
      ],
    })
      .overrideTemplate(RoutingCodeDetailComponent, '')
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RoutingCodeDetailComponent);
    comp = fixture.componentInstance;
  });

  describe('OnInit', () => {
    it('Should load routingCode on init', async () => {
      const harness = await RouterTestingHarness.create();
      const instance = await harness.navigateByUrl('/', RoutingCodeDetailComponent);

      // THEN
      expect(instance.routingCode()).toEqual(expect.objectContaining({ instanceID: 123 }));
    });
  });

  describe('PreviousState', () => {
    it('Should navigate to previous state', () => {
      jest.spyOn(window.history, 'back');
      comp.previousState();
      expect(window.history.back).toHaveBeenCalled();
    });
  });
});
