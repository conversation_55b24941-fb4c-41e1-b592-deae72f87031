<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (routingCode(); as routingCodeRef) {
      <div>
        <h2 data-cy="routingCodeDetailsHeading">
          <span jhiTranslate="legalReferentialFrontApp.routingCode.detail.title">Routing Code</span>
        </h2>

        <hr />

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.instanceID">instanceID</span></dt>
          <dd>
            <span>{{ routingCodeRef.instanceID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeID">Routing Code ID</span></dt>
          <dd>
            <span>{{ routingCodeRef.routingCodeID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.status">Status</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (routingCodeRef.status ?? 'null')">{{
              { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[routingCodeRef.status ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeLabel">Routing Code Label</span></dt>
          <dd>
            <span>{{ routingCodeRef.routingCodeLabel }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.routingCodeType">Routing Code Type</span></dt>
          <dd>
            <span>{{ routingCodeRef.routingCodeType }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.administrativeStatus">Administrative Status</span></dt>
          <dd>
            <span>{{ routingCodeRef.administrativeStatus }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.legalEngagementManagement">Legal Engagement Management</span></dt>
          <dd>
            <span>{{ routingCodeRef.legalEngagementManagement }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressLine1">Address Line 1</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressLine1 }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressLine2">Address Line 2</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressLine2 }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressLine3">Address Line 3</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressLine3 }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressPostalCode">Address Postal Code</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressPostalCode }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressCity">Address City</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressCity }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressCountrySubdivision">Address Country Subdivision</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressCountrySubdivision }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressCountryCode">Address Country Code</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressCountryCode }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.addressCountryLabel">Address Country Label</span></dt>
          <dd>
            <span>{{ routingCodeRef.addressCountryLabel }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.routingCode.fkRoutingCodeSiret">Fk Routing Code Siret</span></dt>
          <dd>
            @if (routingCode()!.fkRoutingCodeSiret) {
              <div>
                <a [routerLink]="['/siret', routingCode()!.fkRoutingCodeSiret?.instanceID, 'view']">{{
                  routingCodeRef.fkRoutingCodeSiret?.instanceID
                }}</a>
              </div>
            }
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Retour</span>
        </button>

        <button type="button" [routerLink]="['/routing-code', routingCodeRef.instanceID, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Editer</span>
        </button>
      </div>
    }
  </div>
</div>
