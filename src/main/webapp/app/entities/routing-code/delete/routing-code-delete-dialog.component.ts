import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import SharedModule from 'app/shared/shared.module';
import { ITEM_DELETED_EVENT } from 'app/config/navigation.constants';
import { IRoutingCode } from '../routing-code.model';
import { RoutingCodeService } from '../service/routing-code.service';

@Component({
  standalone: true,
  templateUrl: './routing-code-delete-dialog.component.html',
  imports: [SharedModule, FormsModule],
})
export class RoutingCodeDeleteDialogComponent {
  routingCode?: IRoutingCode;

  protected routingCodeService = inject(RoutingCodeService);
  protected activeModal = inject(NgbActiveModal);

  cancel(): void {
    this.activeModal.dismiss();
  }

  confirmDelete(instanceID: string): void {
    this.routingCodeService.delete(instanceID).subscribe(() => {
      this.activeModal.close(ITEM_DELETED_EVENT);
    });
  }
}
