@if (routingCode) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(routingCode.instanceID!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="routingCodeDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirmation de suppression</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error></jhi-alert-error>
      <p
        id="jhi-delete-routingCode-heading"
        jhiTranslate="legalReferentialFrontApp.routingCode.delete.question"
        [translateValues]="{ instanceID: routingCode.instanceID }"
      >
        Êtes-vous certain de vouloir supprimer le Routing Code {{ routingCode.instanceID }} ?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
      </button>

      <button id="jhi-confirm-delete-routingCode" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Supprimer</span>
      </button>
    </div>
  </form>
}
