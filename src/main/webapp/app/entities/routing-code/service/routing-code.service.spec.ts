import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';

import { IRoutingCode } from '../routing-code.model';
import { sampleWithFullData, sampleWithNewData, sampleWithPartialData, sampleWithRequiredData } from '../routing-code.test-samples';

import { RoutingCodeService } from './routing-code.service';

const requireRestSample: IRoutingCode = {
  ...sampleWithRequiredData,
};

describe('RoutingCode Service', () => {
  let service: RoutingCodeService;
  let httpMock: HttpTestingController;
  let expectedResult: IRoutingCode | IRoutingCode[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    expectedResult = null;
    service = TestBed.inject(RoutingCodeService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find(123).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a RoutingCode', () => {
      const routingCode = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(routingCode).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a RoutingCode', () => {
      const routingCode = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(routingCode).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a RoutingCode', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of RoutingCode', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a RoutingCode', () => {
      const expected = true;

      service.delete(123).subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    describe('addRoutingCodeToCollectionIfMissing', () => {
      it('should add a RoutingCode to an empty array', () => {
        const routingCode: IRoutingCode = sampleWithRequiredData;
        expectedResult = service.addRoutingCodeToCollectionIfMissing([], routingCode);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(routingCode);
      });

      it('should not add a RoutingCode to an array that contains it', () => {
        const routingCode: IRoutingCode = sampleWithRequiredData;
        const routingCodeCollection: IRoutingCode[] = [
          {
            ...routingCode,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addRoutingCodeToCollectionIfMissing(routingCodeCollection, routingCode);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a RoutingCode to an array that doesn't contain it", () => {
        const routingCode: IRoutingCode = sampleWithRequiredData;
        const routingCodeCollection: IRoutingCode[] = [sampleWithPartialData];
        expectedResult = service.addRoutingCodeToCollectionIfMissing(routingCodeCollection, routingCode);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(routingCode);
      });

      it('should add only unique RoutingCode to an array', () => {
        const routingCodeArray: IRoutingCode[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const routingCodeCollection: IRoutingCode[] = [sampleWithRequiredData];
        expectedResult = service.addRoutingCodeToCollectionIfMissing(routingCodeCollection, ...routingCodeArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const routingCode: IRoutingCode = sampleWithRequiredData;
        const routingCode2: IRoutingCode = sampleWithPartialData;
        expectedResult = service.addRoutingCodeToCollectionIfMissing([], routingCode, routingCode2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(routingCode);
        expect(expectedResult).toContain(routingCode2);
      });

      it('should accept null and undefined values', () => {
        const routingCode: IRoutingCode = sampleWithRequiredData;
        expectedResult = service.addRoutingCodeToCollectionIfMissing([], null, routingCode, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(routingCode);
      });

      it('should return initial array if no RoutingCode is added', () => {
        const routingCodeCollection: IRoutingCode[] = [sampleWithRequiredData];
        expectedResult = service.addRoutingCodeToCollectionIfMissing(routingCodeCollection, undefined, null);
        expect(expectedResult).toEqual(routingCodeCollection);
      });
    });

    describe('compareRoutingCode', () => {
      it('Should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareRoutingCode(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('Should return false if one entity is null', () => {
        const entity1 = { instanceID: 123 };
        const entity2 = null;

        const compareResult1 = service.compareRoutingCode(entity1, entity2);
        const compareResult2 = service.compareRoutingCode(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey differs', () => {
        const entity1 = { instanceID: 123 };
        const entity2 = { instanceID: 456 };

        const compareResult1 = service.compareRoutingCode(entity1, entity2);
        const compareResult2 = service.compareRoutingCode(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey matches', () => {
        const entity1 = { instanceID: 123 };
        const entity2 = { instanceID: 123 };

        const compareResult1 = service.compareRoutingCode(entity1, entity2);
        const compareResult2 = service.compareRoutingCode(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
