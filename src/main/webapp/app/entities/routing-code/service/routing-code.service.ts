import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { IRoutingCode, NewRoutingCode } from '../routing-code.model';

export type PartialUpdateRoutingCode = Partial<IRoutingCode> & Pick<IRoutingCode, 'instanceID'>;

export type EntityResponseType = HttpResponse<IRoutingCode>;
export type EntityArrayResponseType = HttpResponse<IRoutingCode[]>;

@Injectable({ providedIn: 'root' })
export class RoutingCodeService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/routing-codes');

  create(routingCode: NewRoutingCode): Observable<EntityResponseType> {
    return this.http.post<IRoutingCode>(this.resourceUrl, routingCode, { observe: 'response' });
  }

  update(routingCode: IRoutingCode): Observable<EntityResponseType> {
    return this.http.put<IRoutingCode>(`${this.resourceUrl}/${this.getRoutingCodeIdentifier(routingCode)}`, routingCode, {
      observe: 'response',
    });
  }

  partialUpdate(routingCode: PartialUpdateRoutingCode): Observable<EntityResponseType> {
    return this.http.patch<IRoutingCode>(`${this.resourceUrl}/${this.getRoutingCodeIdentifier(routingCode)}`, routingCode, {
      observe: 'response',
    });
  }

  find(instanceID: string): Observable<EntityResponseType> {
    return this.http.get<IRoutingCode>(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<IRoutingCode[]>(this.resourceUrl, { params: options, observe: 'response' });
  }

  delete(instanceID: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  getRoutingCodeIdentifier(routingCode: Pick<IRoutingCode, 'instanceID'>): string {
    return routingCode.instanceID;
  }

  compareRoutingCode(o1: Pick<IRoutingCode, 'instanceID'> | null, o2: Pick<IRoutingCode, 'instanceID'> | null): boolean {
    return o1 && o2 ? this.getRoutingCodeIdentifier(o1) === this.getRoutingCodeIdentifier(o2) : o1 === o2;
  }

  addRoutingCodeToCollectionIfMissing<Type extends Pick<IRoutingCode, 'instanceID'>>(
    routingCodeCollection: Type[],
    ...routingCodesToCheck: (Type | null | undefined)[]
  ): Type[] {
    const routingCodes: Type[] = routingCodesToCheck.filter(isPresent);
    if (routingCodes.length > 0) {
      const routingCodeCollectionIdentifiers = routingCodeCollection.map(routingCodeItem => this.getRoutingCodeIdentifier(routingCodeItem));
      const routingCodesToAdd = routingCodes.filter(routingCodeItem => {
        const routingCodeIdentifier = this.getRoutingCodeIdentifier(routingCodeItem);
        if (routingCodeCollectionIdentifiers.includes(routingCodeIdentifier)) {
          return false;
        }
        routingCodeCollectionIdentifiers.push(routingCodeIdentifier);
        return true;
      });
      return [...routingCodesToAdd, ...routingCodeCollection];
    }
    return routingCodeCollection;
  }
}
