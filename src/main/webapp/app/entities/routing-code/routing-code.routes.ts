import { Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import { ASC } from 'app/config/navigation.constants';
import RoutingCodeResolve from './route/routing-code-routing-resolve.service';

const routingCodeRoute: Routes = [
  {
    path: '',
    loadComponent: () => import('./list/routing-code.component').then(m => m.RoutingCodeComponent),
    data: {
      defaultSort: `instanceID,${ASC}`,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/view',
    loadComponent: () => import('./detail/routing-code-detail.component').then(m => m.RoutingCodeDetailComponent),
    resolve: {
      routingCode: RoutingCodeResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    loadComponent: () => import('./update/routing-code-update.component').then(m => m.RoutingCodeUpdateComponent),
    resolve: {
      routingCode: RoutingCodeResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/edit',
    loadComponent: () => import('./update/routing-code-update.component').then(m => m.RoutingCodeUpdateComponent),
    resolve: {
      routingCode: RoutingCodeResolve,
    },
    canActivate: [UserRouteAccessService],
  },
];

export default routingCodeRoute;
