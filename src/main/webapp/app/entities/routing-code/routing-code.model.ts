import { ISiret } from 'app/entities/siret/siret.model';
import { Statut } from 'app/entities/enumerations/statut.model';

export interface IRoutingCode {
  instanceID: string;
  routingCodeID?: string | null;
  status?: keyof typeof Statut | null;
  routingCodeLabel?: string | null;
  routingCodeType?: string | null;
  administrativeStatus?: string | null;
  legalEngagementManagement?: boolean | null;
  addressLine1?: string | null;
  addressLine2?: string | null;
  addressLine3?: string | null;
  addressPostalCode?: string | null;
  addressCity?: string | null;
  addressCountrySubdivision?: string | null;
  addressCountryCode?: string | null;
  addressCountryLabel?: string | null;
  fkRoutingCodeSiret?: Pick<ISiret, 'instanceID'> | null;
}

export type NewRoutingCode = Omit<IRoutingCode, 'instanceID'> & { instanceID: null };
