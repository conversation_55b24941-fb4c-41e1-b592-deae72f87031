@if (platform) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(platform.instanceID!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="platformDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirmation de suppression</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error></jhi-alert-error>
      <p
        id="jhi-delete-platform-heading"
        jhiTranslate="legalReferentialFrontApp.platform.delete.question"
        [translateValues]="{ instanceID: platform.instanceID }"
      >
        Êtes-vous certain de vouloir supprimer le Platform {{ platform.instanceID }} ?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
      </button>

      <button id="jhi-confirm-delete-platform" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Supprimer</span>
      </button>
    </div>
  </form>
}
