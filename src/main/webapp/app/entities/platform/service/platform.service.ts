import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, map } from 'rxjs';

import dayjs from 'dayjs/esm';

import { isPresent } from 'app/core/util/operators';
import { DATE_FORMAT } from 'app/config/input.constants';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { IPlatform, NewPlatform } from '../platform.model';

export type PartialUpdatePlatform = Partial<IPlatform> & Pick<IPlatform, 'instanceID'>;

type RestOf<T extends IPlatform | NewPlatform> = Omit<T, 'registrationBeginDate' | 'registrationEndDate'> & {
  registrationBeginDate?: string | null;
  registrationEndDate?: string | null;
};

export type RestPlatform = RestOf<IPlatform>;

export type NewRestPlatform = RestOf<NewPlatform>;

export type PartialUpdateRestPlatform = RestOf<PartialUpdatePlatform>;

export type EntityResponseType = HttpResponse<IPlatform>;
export type EntityArrayResponseType = HttpResponse<IPlatform[]>;

@Injectable({ providedIn: 'root' })
export class PlatformService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/platforms');

  create(platform: NewPlatform): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(platform);
    return this.http
      .post<RestPlatform>(this.resourceUrl, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  update(platform: IPlatform): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(platform);
    return this.http
      .put<RestPlatform>(`${this.resourceUrl}/${this.getPlatformIdentifier(platform)}`, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  partialUpdate(platform: PartialUpdatePlatform): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(platform);
    return this.http
      .patch<RestPlatform>(`${this.resourceUrl}/${this.getPlatformIdentifier(platform)}`, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  find(instanceID: string): Observable<EntityResponseType> {
    return this.http
      .get<RestPlatform>(`${this.resourceUrl}/${instanceID}`, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestPlatform[]>(this.resourceUrl, { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }

  delete(instanceID: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  getPlatformIdentifier(platform: Pick<IPlatform, 'instanceID'>): string {
    return platform.instanceID;
  }

  comparePlatform(o1: Pick<IPlatform, 'instanceID'> | null, o2: Pick<IPlatform, 'instanceID'> | null): boolean {
    return o1 && o2 ? this.getPlatformIdentifier(o1) === this.getPlatformIdentifier(o2) : o1 === o2;
  }

  addPlatformToCollectionIfMissing<Type extends Pick<IPlatform, 'instanceID'>>(
    platformCollection: Type[],
    ...platformsToCheck: (Type | null | undefined)[]
  ): Type[] {
    const platforms: Type[] = platformsToCheck.filter(isPresent);
    if (platforms.length > 0) {
      const platformCollectionIdentifiers = platformCollection.map(platformItem => this.getPlatformIdentifier(platformItem));
      const platformsToAdd = platforms.filter(platformItem => {
        const platformIdentifier = this.getPlatformIdentifier(platformItem);
        if (platformCollectionIdentifiers.includes(platformIdentifier)) {
          return false;
        }
        platformCollectionIdentifiers.push(platformIdentifier);
        return true;
      });
      return [...platformsToAdd, ...platformCollection];
    }
    return platformCollection;
  }

  protected convertDateFromClient<T extends IPlatform | NewPlatform | PartialUpdatePlatform>(platform: T): RestOf<T> {
    return {
      ...platform,
      registrationBeginDate: platform.registrationBeginDate?.format(DATE_FORMAT) ?? null,
      registrationEndDate: platform.registrationEndDate?.format(DATE_FORMAT) ?? null,
    };
  }

  protected convertDateFromServer(restPlatform: RestPlatform): IPlatform {
    return {
      ...restPlatform,
      registrationBeginDate: restPlatform.registrationBeginDate ? dayjs(restPlatform.registrationBeginDate) : undefined,
      registrationEndDate: restPlatform.registrationEndDate ? dayjs(restPlatform.registrationEndDate) : undefined,
    };
  }

  protected convertResponseFromServer(res: HttpResponse<RestPlatform>): HttpResponse<IPlatform> {
    return res.clone({
      body: res.body ? this.convertDateFromServer(res.body) : null,
    });
  }

  protected convertResponseArrayFromServer(res: HttpResponse<RestPlatform[]>): HttpResponse<IPlatform[]> {
    return res.clone({
      body: res.body ? res.body.map(item => this.convertDateFromServer(item)) : null,
    });
  }
}
