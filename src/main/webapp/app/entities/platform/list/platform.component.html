<div>
  <h2 id="page-heading" data-cy="PlatformHeading">
    <span jhiTranslate="legalReferentialFrontApp.platform.home.title">Platforms</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.platform.home.refreshListLabel">Actualiser la liste</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-platform"
        [routerLink]="['/platform/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.platform.home.createLabel">Créer un nouveau Platform</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error></jhi-alert-error>

  <jhi-alert></jhi-alert>

  <jhi-filter [filters]="filters"></jhi-filter>

  @if (platforms?.length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="legalReferentialFrontApp.platform.home.notFound">Aucun Platform trouvé</span>
    </div>
  }

  @if (platforms && platforms.length > 0) {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [sortState]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="instanceID">
              <div class="d-flex">
                <span jhiTranslate="global.field.instanceID">instanceID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformID">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformID">Platform ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformType">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformType">Platform Type</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformStatus">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformStatus">Platform Status</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformMatricule">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformMatricule">Platform Matricule</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformSiren">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformSiren">Platform Siren</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformCompanyName">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformCompanyName">Platform Company Name</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformCommercialName">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformCommercialName">Platform Commercial Name</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="platformContactOrUrl">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.platformContactOrUrl">Platform Contact Or Url</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="registrationBeginDate">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.registrationBeginDate">Registration Begin Date</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="registrationEndDate">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.platform.registrationEndDate">Registration End Date</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (platform of platforms; track trackId(platform)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/platform', platform.instanceID, 'view']">{{ platform.instanceID }}</a>
              </td>
              <td>{{ platform.platformID }}</td>
              <td [jhiTranslate]="'legalReferentialFrontApp.PlatformType.' + (platform.platformType ?? 'null')">
                {{ { null: '', PPF: 'PPF', PDP: 'PDP', CPRO: 'CPRO', NA: 'NA' }[platform.platformType ?? 'null'] }}
              </td>
              <td [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (platform.platformStatus ?? 'null')">
                {{ { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[platform.platformStatus ?? 'null'] }}
              </td>
              <td>{{ platform.platformMatricule }}</td>
              <td>{{ platform.platformSiren }}</td>
              <td>{{ platform.platformCompanyName }}</td>
              <td>{{ platform.platformCommercialName }}</td>
              <td>{{ platform.platformContactOrUrl }}</td>
              <td>{{ platform.registrationBeginDate | formatMediumDate }}</td>
              <td>{{ platform.registrationEndDate | formatMediumDate }}</td>
              <td class="text-end">
                <div class="btn-group">
                  <button
                    type="submit"
                    [routerLink]="['/siren']"
                    [queryParams]="{ 'filter[fkSirenPlatformId.in]': platform.instanceID }"
                    class="btn btn-info btn-sm"
                    data-cy="filterOtherEntityButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span
                      class="d-none d-md-inline"
                      jhiTranslate="entity.action.show"
                      [translateValues]="{ otherEntity: ('legalReferentialFrontApp.platform.pkSiren' | translate) }"
                      >Show Pk Siren</span
                    >
                  </button>
                  <a [routerLink]="['/platform', platform.instanceID, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">Voir</span>
                  </a>

                  <a [routerLink]="['/platform', platform.instanceID, 'edit']" class="btn btn-primary btn-sm" data-cy="entityEditButton">
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Editer</span>
                  </a>

                  <button type="submit" (click)="delete(platform)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Supprimer</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
  @if (platforms && platforms.length > 0) {
    <div>
      <div class="d-flex justify-content-center">
        <jhi-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></jhi-item-count>
      </div>

      <div class="d-flex justify-content-center">
        <ngb-pagination
          [collectionSize]="totalItems"
          [page]="page"
          [pageSize]="itemsPerPage"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          (pageChange)="navigateToPage($event)"
        ></ngb-pagination>
      </div>
    </div>
  }
</div>
