import dayjs from 'dayjs/esm';

import { IPlatform, NewPlatform } from './platform.model';

export const sampleWithRequiredData: IPlatform = {
  instanceID: '30230',
};

export const sampleWithPartialData: IPlatform = {
  instanceID: '20660',
  platformID: 'sans que antique',
  platformCommercialName: 'lorsque',
  registrationBeginDate: dayjs('2025-04-18'),
  registrationEndDate: dayjs('2025-04-17'),
};

export const sampleWithFullData: IPlatform = {
  instanceID: '5320',
  platformID: 'accroître du fait que après que',
  platformType: 'PPF',
  platformStatus: 'C',
  platformMatricule: 'commissionnaire vivace',
  platformSiren: 'aigre au-dessus de broum',
  platformCompanyName: 'minuscule habile',
  platformCommercialName: 'souple sitôt que devant',
  platformContactOrUrl: 'croâ aussitôt que démarrer',
  registrationBeginDate: dayjs('2025-04-18'),
  registrationEndDate: dayjs('2025-04-17'),
};

export const sampleWithNewData: NewPlatform = {
  instanceID: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
