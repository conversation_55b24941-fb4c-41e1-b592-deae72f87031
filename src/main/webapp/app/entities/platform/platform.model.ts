import dayjs from 'dayjs/esm';
import { PlatformType } from 'app/entities/enumerations/platform-type.model';
import { Statut } from 'app/entities/enumerations/statut.model';

export interface IPlatform {
  instanceID: string;
  platformID?: string | null;
  platformType?: keyof typeof PlatformType | null;
  platformStatus?: keyof typeof Statut | null;
  platformMatricule?: string | null;
  platformSiren?: string | null;
  platformCompanyName?: string | null;
  platformCommercialName?: string | null;
  platformContactOrUrl?: string | null;
  registrationBeginDate?: dayjs.Dayjs | null;
  registrationEndDate?: dayjs.Dayjs | null;
}

export type NewPlatform = Omit<IPlatform, 'instanceID'> & { instanceID: null };
