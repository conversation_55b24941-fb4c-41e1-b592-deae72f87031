<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2
        id="jhi-platform-heading"
        data-cy="PlatformCreateUpdateHeading"
        jhiTranslate="legalReferentialFrontApp.platform.home.createOrEditLabel"
      >
        Créer ou éditer un Platform
      </h2>

      <div>
        <jhi-alert-error></jhi-alert-error>

        @if (editForm.controls.instanceID.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_instanceID" jhiTranslate="legalReferentialFrontApp.platform.instanceID">instanceID</label>
            <input
              type="number"
              class="form-control"
              name="instanceID"
              id="field_instanceID"
              data-cy="instanceID"
              formControlName="instanceID"
              [readonly]="true"
            />
          </div>
        }

        <div class="mb-3">
          <label class="form-label" for="field_platformID" jhiTranslate="legalReferentialFrontApp.platform.platformID">Platform ID</label>
          <input
            type="text"
            class="form-control"
            name="platformID"
            id="field_platformID"
            data-cy="platformID"
            formControlName="platformID"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_platformType" jhiTranslate="legalReferentialFrontApp.platform.platformType"
            >Platform Type</label
          >
          <select class="form-control" name="platformType" formControlName="platformType" id="field_platformType" data-cy="platformType">
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.PlatformType.null' | translate }}</option>
            @for (platformType of platformTypeValues; track $index) {
              <option [value]="platformType">{{ 'legalReferentialFrontApp.PlatformType.' + platformType | translate }}</option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_platformStatus" jhiTranslate="legalReferentialFrontApp.platform.platformStatus"
            >Platform Status</label
          >
          <select
            class="form-control"
            name="platformStatus"
            formControlName="platformStatus"
            id="field_platformStatus"
            data-cy="platformStatus"
          >
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.Statut.null' | translate }}</option>
            @for (statut of statutValues; track $index) {
              <option [value]="statut">{{ 'legalReferentialFrontApp.Statut.' + statut | translate }}</option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_platformMatricule" jhiTranslate="legalReferentialFrontApp.platform.platformMatricule"
            >Platform Matricule</label
          >
          <input
            type="text"
            class="form-control"
            name="platformMatricule"
            id="field_platformMatricule"
            data-cy="platformMatricule"
            formControlName="platformMatricule"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_platformSiren" jhiTranslate="legalReferentialFrontApp.platform.platformSiren"
            >Platform Siren</label
          >
          <input
            type="text"
            class="form-control"
            name="platformSiren"
            id="field_platformSiren"
            data-cy="platformSiren"
            formControlName="platformSiren"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_platformCompanyName" jhiTranslate="legalReferentialFrontApp.platform.platformCompanyName"
            >Platform Company Name</label
          >
          <input
            type="text"
            class="form-control"
            name="platformCompanyName"
            id="field_platformCompanyName"
            data-cy="platformCompanyName"
            formControlName="platformCompanyName"
          />
        </div>

        <div class="mb-3">
          <label
            class="form-label"
            for="field_platformCommercialName"
            jhiTranslate="legalReferentialFrontApp.platform.platformCommercialName"
            >Platform Commercial Name</label
          >
          <input
            type="text"
            class="form-control"
            name="platformCommercialName"
            id="field_platformCommercialName"
            data-cy="platformCommercialName"
            formControlName="platformCommercialName"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_platformContactOrUrl" jhiTranslate="legalReferentialFrontApp.platform.platformContactOrUrl"
            >Platform Contact Or Url</label
          >
          <input
            type="text"
            class="form-control"
            name="platformContactOrUrl"
            id="field_platformContactOrUrl"
            data-cy="platformContactOrUrl"
            formControlName="platformContactOrUrl"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_registrationBeginDate" jhiTranslate="legalReferentialFrontApp.platform.registrationBeginDate"
            >Registration Begin Date</label
          >
          <div class="input-group">
            <input
              id="field_registrationBeginDate"
              data-cy="registrationBeginDate"
              type="text"
              class="form-control"
              name="registrationBeginDate"
              ngbDatepicker
              #registrationBeginDateDp="ngbDatepicker"
              formControlName="registrationBeginDate"
            />
            <button type="button" class="btn btn-secondary" (click)="registrationBeginDateDp.toggle()">
              <fa-icon icon="calendar-alt"></fa-icon>
            </button>
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_registrationEndDate" jhiTranslate="legalReferentialFrontApp.platform.registrationEndDate"
            >Registration End Date</label
          >
          <div class="input-group">
            <input
              id="field_registrationEndDate"
              data-cy="registrationEndDate"
              type="text"
              class="form-control"
              name="registrationEndDate"
              ngbDatepicker
              #registrationEndDateDp="ngbDatepicker"
              formControlName="registrationEndDate"
            />
            <button type="button" class="btn btn-secondary" (click)="registrationEndDateDp.toggle()">
              <fa-icon icon="calendar-alt"></fa-icon>
            </button>
          </div>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Sauvegarder</span>
        </button>
      </div>
    </form>
  </div>
</div>
