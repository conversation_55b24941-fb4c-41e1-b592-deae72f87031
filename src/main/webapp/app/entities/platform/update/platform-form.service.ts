import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { IPlatform, NewPlatform } from '../platform.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { instanceID: unknown }> = Partial<Omit<T, 'instanceID'>> & { instanceID: T['instanceID'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IPlatform for edit and NewPlatformFormGroupInput for create.
 */
type PlatformFormGroupInput = IPlatform | PartialWithRequiredKeyOf<NewPlatform>;

type PlatformFormDefaults = Pick<NewPlatform, 'instanceID'>;

type PlatformFormGroupContent = {
  instanceID: FormControl<IPlatform['instanceID'] | NewPlatform['instanceID']>;
  platformID: FormControl<IPlatform['platformID']>;
  platformType: FormControl<IPlatform['platformType']>;
  platformStatus: FormControl<IPlatform['platformStatus']>;
  platformMatricule: FormControl<IPlatform['platformMatricule']>;
  platformSiren: FormControl<IPlatform['platformSiren']>;
  platformCompanyName: FormControl<IPlatform['platformCompanyName']>;
  platformCommercialName: FormControl<IPlatform['platformCommercialName']>;
  platformContactOrUrl: FormControl<IPlatform['platformContactOrUrl']>;
  registrationBeginDate: FormControl<IPlatform['registrationBeginDate']>;
  registrationEndDate: FormControl<IPlatform['registrationEndDate']>;
};

export type PlatformFormGroup = FormGroup<PlatformFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class PlatformFormService {
  createPlatformFormGroup(platform: PlatformFormGroupInput = { instanceID: null }): PlatformFormGroup {
    const platformRawValue = {
      ...this.getFormDefaults(),
      ...platform,
    };
    return new FormGroup<PlatformFormGroupContent>({
      instanceID: new FormControl(
        { value: platformRawValue.instanceID, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      platformID: new FormControl(platformRawValue.platformID),
      platformType: new FormControl(platformRawValue.platformType),
      platformStatus: new FormControl(platformRawValue.platformStatus),
      platformMatricule: new FormControl(platformRawValue.platformMatricule),
      platformSiren: new FormControl(platformRawValue.platformSiren),
      platformCompanyName: new FormControl(platformRawValue.platformCompanyName),
      platformCommercialName: new FormControl(platformRawValue.platformCommercialName),
      platformContactOrUrl: new FormControl(platformRawValue.platformContactOrUrl),
      registrationBeginDate: new FormControl(platformRawValue.registrationBeginDate),
      registrationEndDate: new FormControl(platformRawValue.registrationEndDate),
    });
  }

  getPlatform(form: PlatformFormGroup): IPlatform | NewPlatform {
    return form.getRawValue() as IPlatform | NewPlatform;
  }

  resetForm(form: PlatformFormGroup, platform: PlatformFormGroupInput): void {
    const platformRawValue = { ...this.getFormDefaults(), ...platform };
    form.reset(
      {
        ...platformRawValue,
        instanceID: { value: platformRawValue.instanceID, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): PlatformFormDefaults {
    return {
      instanceID: null,
    };
  }
}
