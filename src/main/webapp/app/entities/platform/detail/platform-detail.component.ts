import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { DurationPipe, FormatMediumDatePipe, FormatMediumDatetimePipe } from 'app/shared/date';
import { IPlatform } from '../platform.model';

@Component({
  standalone: true,
  selector: 'jhi-platform-detail',
  templateUrl: './platform-detail.component.html',
  imports: [SharedModule, RouterModule, DurationPipe, FormatMediumDatetimePipe, FormatMediumDatePipe],
})
export class PlatformDetailComponent {
  platform = input<IPlatform | null>(null);

  previousState(): void {
    window.history.back();
  }
}
