<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (platform(); as platformRef) {
      <div>
        <h2 data-cy="platformDetailsHeading"><span jhiTranslate="legalReferentialFrontApp.platform.detail.title">Platform</span></h2>

        <hr />

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.instanceID">instanceID</span></dt>
          <dd>
            <span>{{ platformRef.instanceID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformID">Platform ID</span></dt>
          <dd>
            <span>{{ platformRef.platformID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformType">Platform Type</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.PlatformType.' + (platformRef.platformType ?? 'null')">{{
              { null: '', PPF: 'PPF', PDP: 'PDP', CPRO: 'CPRO', NA: 'NA' }[platformRef.platformType ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformStatus">Platform Status</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (platformRef.platformStatus ?? 'null')">{{
              { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[platformRef.platformStatus ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformMatricule">Platform Matricule</span></dt>
          <dd>
            <span>{{ platformRef.platformMatricule }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformSiren">Platform Siren</span></dt>
          <dd>
            <span>{{ platformRef.platformSiren }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformCompanyName">Platform Company Name</span></dt>
          <dd>
            <span>{{ platformRef.platformCompanyName }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformCommercialName">Platform Commercial Name</span></dt>
          <dd>
            <span>{{ platformRef.platformCommercialName }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.platformContactOrUrl">Platform Contact Or Url</span></dt>
          <dd>
            <span>{{ platformRef.platformContactOrUrl }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.registrationBeginDate">Registration Begin Date</span></dt>
          <dd>
            <span>{{ platformRef.registrationBeginDate | formatMediumDate }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.platform.registrationEndDate">Registration End Date</span></dt>
          <dd>
            <span>{{ platformRef.registrationEndDate | formatMediumDate }}</span>
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Retour</span>
        </button>

        <button type="button" [routerLink]="['/platform', platformRef.instanceID, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Editer</span>
        </button>
      </div>
    }
  </div>
</div>
