import { TypeEntiteDestinataire } from 'app/entities/enumerations/type-entite-destinataire.model';
import { Statut } from 'app/entities/enumerations/statut.model';

export interface ISiren {
  instanceID: string;
  siren?: string | null;
  companyName?: string | null;
  entityType?: keyof typeof TypeEntiteDestinataire | null;
  status?: keyof typeof Statut | null;
}

export type NewSiren = Omit<ISiren, 'instanceID'> & { instanceID: null };
