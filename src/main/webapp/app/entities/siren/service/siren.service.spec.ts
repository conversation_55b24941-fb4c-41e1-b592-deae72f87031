import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';

import { ISiren } from '../siren.model';
import { sampleWithFullData, sampleWithNewData, sampleWithPartialData, sampleWithRequiredData } from '../siren.test-samples';

import { SirenService } from './siren.service';

const requireRestSample: ISiren = {
  ...sampleWithRequiredData,
};

describe('Siren Service', () => {
  let service: SirenService;
  let httpMock: HttpTestingController;
  let expectedResult: ISiren | ISiren[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    expectedResult = null;
    service = TestBed.inject(SirenService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find('123').subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a Siren', () => {
      const siren = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(siren).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a Siren', () => {
      const siren = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(siren).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a Siren', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of Siren', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a Siren', () => {
      const expected = true;

      service.delete('123').subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    describe('addSirenToCollectionIfMissing', () => {
      it('should add a Siren to an empty array', () => {
        const siren: ISiren = sampleWithRequiredData;
        expectedResult = service.addSirenToCollectionIfMissing([], siren);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(siren);
      });

      it('should not add a Siren to an array that contains it', () => {
        const siren: ISiren = sampleWithRequiredData;
        const sirenCollection: ISiren[] = [
          {
            ...siren,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addSirenToCollectionIfMissing(sirenCollection, siren);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a Siren to an array that doesn't contain it", () => {
        const siren: ISiren = sampleWithRequiredData;
        const sirenCollection: ISiren[] = [sampleWithPartialData];
        expectedResult = service.addSirenToCollectionIfMissing(sirenCollection, siren);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(siren);
      });

      it('should add only unique Siren to an array', () => {
        const sirenArray: ISiren[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const sirenCollection: ISiren[] = [sampleWithRequiredData];
        expectedResult = service.addSirenToCollectionIfMissing(sirenCollection, ...sirenArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const siren: ISiren = sampleWithRequiredData;
        const siren2: ISiren = sampleWithPartialData;
        expectedResult = service.addSirenToCollectionIfMissing([], siren, siren2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(siren);
        expect(expectedResult).toContain(siren2);
      });

      it('should accept null and undefined values', () => {
        const siren: ISiren = sampleWithRequiredData;
        expectedResult = service.addSirenToCollectionIfMissing([], null, siren, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(siren);
      });

      it('should return initial array if no Siren is added', () => {
        const sirenCollection: ISiren[] = [sampleWithRequiredData];
        expectedResult = service.addSirenToCollectionIfMissing(sirenCollection, undefined, null);
        expect(expectedResult).toEqual(sirenCollection);
      });
    });

    describe('compareSiren', () => {
      it('Should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareSiren(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('Should return false if one entity is null', () => {
        const entity1 = { instanceID: '123' };
        const entity2 = null;

        const compareResult1 = service.compareSiren(entity1, entity2);
        const compareResult2 = service.compareSiren(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey differs', () => {
        const entity1 = { instanceID: '123' };
        const entity2 = { instanceID: '456' };

        const compareResult1 = service.compareSiren(entity1, entity2);
        const compareResult2 = service.compareSiren(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('Should return false if primaryKey matches', () => {
        const entity1 = { instanceID: '123' };
        const entity2 = { instanceID: '123' };

        const compareResult1 = service.compareSiren(entity1, entity2);
        const compareResult2 = service.compareSiren(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
