import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { ISiren, NewSiren } from '../siren.model';

export type PartialUpdateSiren = Partial<ISiren> & Pick<ISiren, 'instanceID'>;

export type EntityResponseType = HttpResponse<ISiren>;
export type EntityArrayResponseType = HttpResponse<ISiren[]>;

@Injectable({ providedIn: 'root' })
export class SirenService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/sirens');

  create(siren: NewSiren): Observable<EntityResponseType> {
    return this.http.post<ISiren>(this.resourceUrl, siren, { observe: 'response' });
  }

  update(siren: ISiren): Observable<EntityResponseType> {
    return this.http.put<ISiren>(`${this.resourceUrl}/${this.getSirenIdentifier(siren)}`, siren, { observe: 'response' });
  }

  partialUpdate(siren: PartialUpdateSiren): Observable<EntityResponseType> {
    return this.http.patch<ISiren>(`${this.resourceUrl}/${this.getSirenIdentifier(siren)}`, siren, { observe: 'response' });
  }

  find(instanceID: string): Observable<EntityResponseType> {
    return this.http.get<ISiren>(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<ISiren[]>(this.resourceUrl, { params: options, observe: 'response' });
  }

  delete(instanceID: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${instanceID}`, { observe: 'response' });
  }

  getSirenIdentifier(siren: Pick<ISiren, 'instanceID'>): string {
    return siren.instanceID;
  }

  compareSiren(o1: Pick<ISiren, 'instanceID'> | null, o2: Pick<ISiren, 'instanceID'> | null): boolean {
    return o1 && o2 ? this.getSirenIdentifier(o1) === this.getSirenIdentifier(o2) : o1 === o2;
  }

  addSirenToCollectionIfMissing<Type extends Pick<ISiren, 'instanceID'>>(
    sirenCollection: Type[],
    ...sirensToCheck: (Type | null | undefined)[]
  ): Type[] {
    const sirens: Type[] = sirensToCheck.filter(isPresent);
    if (sirens.length > 0) {
      const sirenCollectionIdentifiers = sirenCollection.map(sirenItem => this.getSirenIdentifier(sirenItem));
      const sirensToAdd = sirens.filter(sirenItem => {
        const sirenIdentifier = this.getSirenIdentifier(sirenItem);
        if (sirenCollectionIdentifiers.includes(sirenIdentifier)) {
          return false;
        }
        sirenCollectionIdentifiers.push(sirenIdentifier);
        return true;
      });
      return [...sirensToAdd, ...sirenCollection];
    }
    return sirenCollection;
  }
}
