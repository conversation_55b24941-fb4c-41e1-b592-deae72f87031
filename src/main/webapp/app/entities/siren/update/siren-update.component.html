<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2 id="jhi-siren-heading" data-cy="SirenCreateUpdateHeading" jhiTranslate="legalReferentialFrontApp.siren.home.createOrEditLabel">
        <PERSON><PERSON>er ou éditer un Siren
      </h2>

      <div>
        <jhi-alert-error></jhi-alert-error>

        @if (editForm.controls.instanceID.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_instanceID" jhiTranslate="legalReferentialFrontApp.siren.instanceID">ID</label>
            <input
              type="text"
              class="form-control"
              name="instanceID"
              id="field_instanceID"
              data-cy="instanceID"
              formControlName="instanceID"
              [readonly]="true"
            />
          </div>
        }

        <div class="mb-3">
          <label class="form-label" for="field_siren" jhiTranslate="legalReferentialFrontApp.siren.siren">Siren</label>
          <input type="text" class="form-control" name="siren" id="field_siren" data-cy="siren" formControlName="siren" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_companyName" jhiTranslate="legalReferentialFrontApp.siren.companyName">Company Name</label>
          <input
            type="text"
            class="form-control"
            name="companyName"
            id="field_companyName"
            data-cy="companyName"
            formControlName="companyName"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_entityType" jhiTranslate="legalReferentialFrontApp.siren.entityType">Entity Type</label>
          <select class="form-control" name="entityType" formControlName="entityType" id="field_entityType" data-cy="entityType">
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.TypeEntiteDestinataire.null' | translate }}</option>
            @for (typeEntiteDestinataire of typeEntiteDestinataireValues; track $index) {
              <option [value]="typeEntiteDestinataire">
                {{ 'legalReferentialFrontApp.TypeEntiteDestinataire.' + typeEntiteDestinataire | translate }}
              </option>
            }
          </select>
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_status" jhiTranslate="legalReferentialFrontApp.siren.status">Status</label>
          <select class="form-control" name="status" formControlName="status" id="field_status" data-cy="status">
            <option [ngValue]="null">{{ 'legalReferentialFrontApp.Statut.null' | translate }}</option>
            @for (statut of statutValues; track $index) {
              <option [value]="statut">{{ 'legalReferentialFrontApp.Statut.' + statut | translate }}</option>
            }
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Sauvegarder</span>
        </button>
      </div>
    </form>
  </div>
</div>
