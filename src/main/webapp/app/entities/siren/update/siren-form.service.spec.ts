import { TestBed } from '@angular/core/testing';

import { sampleWithNewData, sampleWithRequiredData } from '../siren.test-samples';

import { SirenFormService } from './siren-form.service';

describe('Siren Form Service', () => {
  let service: SirenFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SirenFormService);
  });

  describe('Service methods', () => {
    describe('createSirenFormGroup', () => {
      it('should create a new form with FormControl', () => {
        const formGroup = service.createSirenFormGroup();

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            siren: expect.any(Object),
            companyName: expect.any(Object),
            entityType: expect.any(Object),
            status: expect.any(Object),
          }),
        );
      });

      it('passing ISiren should create a new form with FormGroup', () => {
        const formGroup = service.createSirenFormGroup(sampleWithRequiredData);

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            instanceID: expect.any(Object),
            siren: expect.any(Object),
            companyName: expect.any(Object),
            entityType: expect.any(Object),
            status: expect.any(Object),
          }),
        );
      });
    });

    describe('getSiren', () => {
      it('should return NewSiren for default Siren initial value', () => {
        const formGroup = service.createSirenFormGroup(sampleWithNewData);

        const siren = service.getSiren(formGroup) as any;

        expect(siren).toMatchObject(sampleWithNewData);
      });

      it('should return NewSiren for empty Siren initial value', () => {
        const formGroup = service.createSirenFormGroup();

        const siren = service.getSiren(formGroup) as any;

        expect(siren).toMatchObject({});
      });

      it('should return ISiren', () => {
        const formGroup = service.createSirenFormGroup(sampleWithRequiredData);

        const siren = service.getSiren(formGroup) as any;

        expect(siren).toMatchObject(sampleWithRequiredData);
      });
    });

    describe('resetForm', () => {
      it('passing ISiren should not enable id FormControl', () => {
        const formGroup = service.createSirenFormGroup();
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, sampleWithRequiredData);

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });

      it('passing NewSiren should disable id FormControl', () => {
        const formGroup = service.createSirenFormGroup(sampleWithRequiredData);
        expect(formGroup.controls.instanceID.disabled).toBe(true);

        service.resetForm(formGroup, { instanceID: null });

        expect(formGroup.controls.instanceID.disabled).toBe(true);
      });
    });
  });
});
