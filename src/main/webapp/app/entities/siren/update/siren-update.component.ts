import { Component, OnInit, inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TypeEntiteDestinataire } from 'app/entities/enumerations/type-entite-destinataire.model';
import { Statut } from 'app/entities/enumerations/statut.model';
import { ISiren } from '../siren.model';
import { SirenService } from '../service/siren.service';
import { SirenFormGroup, SirenFormService } from './siren-form.service';

@Component({
  standalone: true,
  selector: 'jhi-siren-update',
  templateUrl: './siren-update.component.html',
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class SirenUpdateComponent implements OnInit {
  isSaving = false;
  siren: ISiren | null = null;
  typeEntiteDestinataireValues = Object.keys(TypeEntiteDestinataire);
  statutValues = Object.keys(Statut);

  protected sirenService = inject(SirenService);
  protected sirenFormService = inject(SirenFormService);
  protected activatedRoute = inject(ActivatedRoute);

  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: SirenFormGroup = this.sirenFormService.createSirenFormGroup();

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ siren }) => {
      this.siren = siren;
      if (siren) {
        this.updateForm(siren);
      }
    });
  }

  previousState(): void {
    window.history.back();
  }

  save(): void {
    this.isSaving = true;
    const siren = this.sirenFormService.getSiren(this.editForm);
    if (siren.instanceID !== null) {
      this.subscribeToSaveResponse(this.sirenService.update(siren));
    } else {
      this.subscribeToSaveResponse(this.sirenService.create(siren));
    }
  }

  protected subscribeToSaveResponse(result: Observable<HttpResponse<ISiren>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSaving = false;
  }

  protected updateForm(siren: ISiren): void {
    this.siren = siren;
    this.sirenFormService.resetForm(this.editForm, siren);
  }
}
