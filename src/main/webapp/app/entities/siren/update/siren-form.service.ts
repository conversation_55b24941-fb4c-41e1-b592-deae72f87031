import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { ISiren, NewSiren } from '../siren.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { instanceID: unknown }> = Partial<Omit<T, 'instanceID'>> & { instanceID: T['instanceID'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts ISiren for edit and NewSirenFormGroupInput for create.
 */
type SirenFormGroupInput = ISiren | PartialWithRequiredKeyOf<NewSiren>;

type SirenFormDefaults = Pick<NewSiren, 'instanceID'>;

type SirenFormGroupContent = {
  instanceID: FormControl<ISiren['instanceID'] | null>;
  siren: FormControl<ISiren['siren']>;
  companyName: FormControl<ISiren['companyName']>;
  entityType: FormControl<ISiren['entityType']>;
  status: FormControl<ISiren['status']>;
};

export type SirenFormGroup = FormGroup<SirenFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class SirenFormService {
  createSirenFormGroup(siren: SirenFormGroupInput = { instanceID: null }): SirenFormGroup {
    const sirenRawValue = {
      ...this.getFormDefaults(),
      ...siren,
    };
    return new FormGroup<SirenFormGroupContent>({
      instanceID: new FormControl(
        { value: sirenRawValue.instanceID, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      siren: new FormControl(sirenRawValue.siren),
      companyName: new FormControl(sirenRawValue.companyName),
      entityType: new FormControl(sirenRawValue.entityType),
      status: new FormControl(sirenRawValue.status),
    });
  }

  getSiren(form: SirenFormGroup): ISiren | NewSiren {
    return form.getRawValue() as ISiren | NewSiren;
  }

  resetForm(form: SirenFormGroup, siren: SirenFormGroupInput): void {
    const sirenRawValue = { ...this.getFormDefaults(), ...siren };
    form.reset(
      {
        ...sirenRawValue,
        instanceID: { value: sirenRawValue.instanceID, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): SirenFormDefaults {
    return {
      instanceID: null,
    };
  }
}
