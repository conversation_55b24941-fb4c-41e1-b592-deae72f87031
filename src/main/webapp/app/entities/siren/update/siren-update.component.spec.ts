import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subject, from, of } from 'rxjs';

import { SirenService } from '../service/siren.service';
import { ISiren } from '../siren.model';
import { SirenFormService } from './siren-form.service';

import { SirenUpdateComponent } from './siren-update.component';

describe('Siren Management Update Component', () => {
  let comp: SirenUpdateComponent;
  let fixture: ComponentFixture<SirenUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let sirenFormService: SirenFormService;
  let sirenService: SirenService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [SirenUpdateComponent],
      providers: [
        provideHttpClient(),
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(SirenUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(SirenUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    sirenFormService = TestBed.inject(SirenFormService);
    sirenService = TestBed.inject(SirenService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('Should update editForm', () => {
      const siren: ISiren = { instanceID: 456 };

      activatedRoute.data = of({ siren });
      comp.ngOnInit();

      expect(comp.siren).toEqual(siren);
    });
  });

  describe('save', () => {
    it('Should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<ISiren>>();
      const siren = { instanceID: 123 };
      jest.spyOn(sirenFormService, 'getSiren').mockReturnValue(siren);
      jest.spyOn(sirenService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ siren });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: siren }));
      saveSubject.complete();

      // THEN
      expect(sirenFormService.getSiren).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(sirenService.update).toHaveBeenCalledWith(expect.objectContaining(siren));
      expect(comp.isSaving).toEqual(false);
    });

    it('Should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<ISiren>>();
      const siren = { instanceID: 123 };
      jest.spyOn(sirenFormService, 'getSiren').mockReturnValue({ instanceID: null });
      jest.spyOn(sirenService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ siren: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: siren }));
      saveSubject.complete();

      // THEN
      expect(sirenFormService.getSiren).toHaveBeenCalled();
      expect(sirenService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('Should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<ISiren>>();
      const siren = { instanceID: 123 };
      jest.spyOn(sirenService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ siren });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(sirenService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });
});
