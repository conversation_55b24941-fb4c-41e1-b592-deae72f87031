import { ISiren, NewSiren } from './siren.model';

export const sampleWithRequiredData: ISiren = {
  instanceID: '22569',
};

export const sampleWithPartialData: ISiren = {
  instanceID: '2752',
  companyName: 'actionnaire',
  entityType: 'ASSUJETTI',
  status: 'I',
};

export const sampleWithFullData: ISiren = {
  instanceID: '15792',
  siren: 'tracer pour que',
  companyName: 'à partir de',
  entityType: 'PUBLIQUE',
  status: 'F',
};

export const sampleWithNewData: NewSiren = {
  instanceID: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
