import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { of } from 'rxjs';

import { SirenDetailComponent } from './siren-detail.component';

describe('Siren Management Detail Component', () => {
  let comp: SirenDetailComponent;
  let fixture: ComponentFixture<SirenDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SirenDetailComponent],
      providers: [
        provideRouter(
          [
            {
              path: '**',
              loadComponent: () => import('./siren-detail.component').then(m => m.SirenDetailComponent),
              resolve: { siren: () => of({ instanceID: 123 }) },
            },
          ],
          withComponentInputBinding(),
        ),
      ],
    })
      .overrideTemplate(SirenDetailComponent, '')
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SirenDetailComponent);
    comp = fixture.componentInstance;
  });

  describe('OnInit', () => {
    it('Should load siren on init', async () => {
      const harness = await RouterTestingHarness.create();
      const instance = await harness.navigateByUrl('/', SirenDetailComponent);

      // THEN
      expect(instance.siren()).toEqual(expect.objectContaining({ instanceID: 123 }));
    });
  });

  describe('PreviousState', () => {
    it('Should navigate to previous state', () => {
      jest.spyOn(window.history, 'back');
      comp.previousState();
      expect(window.history.back).toHaveBeenCalled();
    });
  });
});
