import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { DurationPipe, FormatMediumDatePipe, FormatMediumDatetimePipe } from 'app/shared/date';
import { ISiren } from '../siren.model';

@Component({
  standalone: true,
  selector: 'jhi-siren-detail',
  templateUrl: './siren-detail.component.html',
  imports: [SharedModule, RouterModule, DurationPipe, FormatMediumDatetimePipe, FormatMediumDatePipe],
})
export class SirenDetailComponent {
  siren = input<ISiren | null>(null);

  previousState(): void {
    window.history.back();
  }
}
