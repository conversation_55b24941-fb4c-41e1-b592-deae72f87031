<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (siren(); as sirenRef) {
      <div>
        <h2 data-cy="sirenDetailsHeading"><span jhiTranslate="legalReferentialFrontApp.siren.detail.title">Siren</span></h2>

        <hr />

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.instanceID">ID</span></dt>
          <dd>
            <span>{{ sirenRef.instanceID }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siren.siren">Siren</span></dt>
          <dd>
            <span>{{ sirenRef.siren }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siren.companyName">Company Name</span></dt>
          <dd>
            <span>{{ sirenRef.companyName }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siren.entityType">Entity Type</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.TypeEntiteDestinataire.' + (sirenRef.entityType ?? 'null')">{{
              { null: '', PUBLIQUE: 'PUBLIQUE', ASSUJETTI: 'ASSUJETTI' }[sirenRef.entityType ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="legalReferentialFrontApp.siren.status">Status</span></dt>
          <dd>
            <span [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (sirenRef.status ?? 'null')">{{
              { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[sirenRef.status ?? 'null']
            }}</span>
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Retour</span>
        </button>

        <button type="button" [routerLink]="['/siren', sirenRef.instanceID, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Editer</span>
        </button>
      </div>
    }
  </div>
</div>
