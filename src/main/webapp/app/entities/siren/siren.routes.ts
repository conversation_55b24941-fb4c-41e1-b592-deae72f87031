import { Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import { ASC } from 'app/config/navigation.constants';
import SirenResolve from './route/siren-routing-resolve.service';

const sirenRoute: Routes = [
  {
    path: '',
    loadComponent: () => import('./list/siren.component').then(m => m.SirenComponent),
    data: {
      defaultSort: `instanceID,${ASC}`,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/view',
    loadComponent: () => import('./detail/siren-detail.component').then(m => m.SirenDetailComponent),
    resolve: {
      siren: SirenResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    loadComponent: () => import('./update/siren-update.component').then(m => m.SirenUpdateComponent),
    resolve: {
      siren: SirenResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':instanceID/edit',
    loadComponent: () => import('./update/siren-update.component').then(m => m.SirenUpdateComponent),
    resolve: {
      siren: SirenResolve,
    },
    canActivate: [UserRouteAccessService],
  },
];

export default sirenRoute;
