<div>
  <h2 id="page-heading" data-cy="SirenHeading">
    <span jhiTranslate="legalReferentialFrontApp.siren.home.title">Sirens</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.siren.home.refreshListLabel">Actualiser la liste</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-siren"
        [routerLink]="['/siren/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="legalReferentialFrontApp.siren.home.createLabel">Créer un nouveau Siren</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error></jhi-alert-error>

  <jhi-alert></jhi-alert>

  <jhi-filter [filters]="filters"></jhi-filter>

  @if (sirens?.length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="legalReferentialFrontApp.siren.home.notFound">Aucun Siren trouvé</span>
    </div>
  }

  @if (sirens && sirens.length > 0) {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [sortState]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="instanceID">
              <div class="d-flex">
                <span jhiTranslate="global.field.instanceID">ID</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="siren">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siren.siren">Siren</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="companyName">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siren.companyName">Company Name</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="entityType">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siren.entityType">Entity Type</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="status">
              <div class="d-flex">
                <span jhiTranslate="legalReferentialFrontApp.siren.status">Status</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (siren of sirens; track trackId(siren)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/siren', siren.instanceID, 'view']">{{ siren.instanceID }}</a>
              </td>
              <td>{{ siren.siren }}</td>
              <td>{{ siren.companyName }}</td>
              <td [jhiTranslate]="'legalReferentialFrontApp.TypeEntiteDestinataire.' + (siren.entityType ?? 'null')">
                {{ { null: '', PUBLIQUE: 'PUBLIQUE', ASSUJETTI: 'ASSUJETTI' }[siren.entityType ?? 'null'] }}
              </td>
              <td [jhiTranslate]="'legalReferentialFrontApp.Statut.' + (siren.status ?? 'null')">
                {{ { null: '', A: 'A', I: 'I', F: 'F', C: 'C' }[siren.status ?? 'null'] }}
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <button
                    type="submit"
                    [routerLink]="['/siret']"
                    [queryParams]="{ 'filter[fkSiretSirenId.in]': siren.instanceID }"
                    class="btn btn-info btn-sm"
                    data-cy="filterOtherEntityButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span
                      class="d-none d-md-inline"
                      jhiTranslate="entity.action.show"
                      [translateValues]="{ otherEntity: ('legalReferentialFrontApp.siren.pkSiret' | translate) }"
                      >Show Pk Siret</span
                    >
                  </button>
                  <button
                    type="submit"
                    [routerLink]="['/address-line']"
                    [queryParams]="{ 'filter[fkAddressLineSirenId.in]': siren.instanceID }"
                    class="btn btn-info btn-sm"
                    data-cy="filterOtherEntityButton"
                  >
                    <fa-icon icon="eye"></fa-icon>
                    <span
                      class="d-none d-md-inline"
                      jhiTranslate="entity.action.show"
                      [translateValues]="{ otherEntity: ('legalReferentialFrontApp.siren.pkAddressLine' | translate) }"
                      >Show Pk Address Line</span
                    >
                  </button>
                  <a [routerLink]="['/siren', siren.instanceID, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">Voir</span>
                  </a>

                  <a [routerLink]="['/siren', siren.instanceID, 'edit']" class="btn btn-primary btn-sm" data-cy="entityEditButton">
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Editer</span>
                  </a>

                  <button type="submit" (click)="delete(siren)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Supprimer</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
  @if (sirens && sirens.length > 0) {
    <div>
      <div class="d-flex justify-content-center">
        <jhi-item-count [params]="{ page: page, totalItems: totalItems, itemsPerPage: itemsPerPage }"></jhi-item-count>
      </div>

      <div class="d-flex justify-content-center">
        <ngb-pagination
          [collectionSize]="totalItems"
          [page]="page"
          [pageSize]="itemsPerPage"
          [maxSize]="5"
          [rotate]="true"
          [boundaryLinks]="true"
          (pageChange)="navigateToPage($event)"
        ></ngb-pagination>
      </div>
    </div>
  }
</div>
