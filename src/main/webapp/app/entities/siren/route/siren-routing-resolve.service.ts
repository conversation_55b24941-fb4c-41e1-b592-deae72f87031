import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { ISiren } from '../siren.model';
import { SirenService } from '../service/siren.service';

const sirenResolve = (route: ActivatedRouteSnapshot): Observable<null | ISiren> => {
  const instanceID = route.params.instanceID;
  if (instanceID) {
    return inject(SirenService)
      .find(instanceID)
      .pipe(
        mergeMap((siren: HttpResponse<ISiren>) => {
          if (siren.body) {
            return of(siren.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default sirenResolve;
