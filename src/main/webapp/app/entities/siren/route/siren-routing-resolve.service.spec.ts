import { TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { ActivatedRoute, ActivatedRouteSnapshot, Router, convertToParamMap } from '@angular/router';
import { of } from 'rxjs';

import { ISiren } from '../siren.model';
import { SirenService } from '../service/siren.service';

import sirenResolve from './siren-routing-resolve.service';

describe('Siren routing resolve service', () => {
  let mockRouter: Router;
  let mockActivatedRouteSnapshot: ActivatedRouteSnapshot;
  let service: SirenService;
  let resultSiren: ISiren | null | undefined;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: convertToParamMap({}),
            },
          },
        },
      ],
    });
    mockRouter = TestBed.inject(Router);
    jest.spyOn(mockRouter, 'navigate').mockImplementation(() => Promise.resolve(true));
    mockActivatedRouteSnapshot = TestBed.inject(ActivatedRoute).snapshot;
    service = TestBed.inject(SirenService);
    resultSiren = undefined;
  });

  describe('resolve', () => {
    it('should return ISiren returned by find', () => {
      // GIVEN
      service.find = jest.fn(instanceID => of(new HttpResponse({ body: { instanceID } })));
      mockActivatedRouteSnapshot.params = { instanceID: '123' };

      // WHEN
      TestBed.runInInjectionContext(() => {
        sirenResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultSiren = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultSiren).toEqual({ instanceID: '123' });
    });

    it('should return null if id is not provided', () => {
      // GIVEN
      service.find = jest.fn();
      mockActivatedRouteSnapshot.params = {};

      // WHEN
      TestBed.runInInjectionContext(() => {
        sirenResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultSiren = result;
          },
        });
      });

      // THEN
      expect(service.find).not.toHaveBeenCalled();
      expect(resultSiren).toEqual(null);
    });

    it('should route to 404 page if data not found in server', () => {
      // GIVEN
      jest.spyOn(service, 'find').mockReturnValue(of(new HttpResponse<ISiren>({ body: null })));
      mockActivatedRouteSnapshot.params = { instanceID: '123' };

      // WHEN
      TestBed.runInInjectionContext(() => {
        sirenResolve(mockActivatedRouteSnapshot).subscribe({
          next(result) {
            resultSiren = result;
          },
        });
      });

      // THEN
      expect(service.find).toHaveBeenCalledWith(123);
      expect(resultSiren).toEqual(undefined);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['404']);
    });
  });
});
