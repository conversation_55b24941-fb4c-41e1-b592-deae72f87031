import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import SharedModule from 'app/shared/shared.module';
import { ITEM_DELETED_EVENT } from 'app/config/navigation.constants';
import { ISiren } from '../siren.model';
import { SirenService } from '../service/siren.service';

@Component({
  standalone: true,
  templateUrl: './siren-delete-dialog.component.html',
  imports: [SharedModule, FormsModule],
})
export class SirenDeleteDialogComponent {
  siren?: ISiren;

  protected sirenService = inject(SirenService);
  protected activeModal = inject(NgbActiveModal);

  cancel(): void {
    this.activeModal.dismiss();
  }

  confirmDelete(instanceID: string): void {
    this.sirenService.delete(instanceID).subscribe(() => {
      this.activeModal.close(ITEM_DELETED_EVENT);
    });
  }
}
