@if (siren) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(siren.instanceID!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="sirenDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirmation de suppression</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error></jhi-alert-error>
      <p
        id="jhi-delete-siren-heading"
        jhiTranslate="legalReferentialFrontApp.siren.delete.question"
        [translateValues]="{ instanceID: siren.instanceID }"
      >
        Êtes-vous certain de vouloir supprimer le Siren {{ siren.instanceID }} ?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Annuler</span>
      </button>

      <button id="jhi-confirm-delete-siren" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Supprimer</span>
      </button>
    </div>
  </form>
}
