import { inject, Injectable } from '@angular/core';
import { from } from 'rxjs';
import { AccountService } from 'app/core/auth/account.service';
import { KeycloakService } from 'keycloak-angular';

@Injectable({ providedIn: 'root' })
export class LoginService {
  private readonly accountService = inject(AccountService);
  private readonly keycloak: KeycloakService = inject(KeycloakService);

  login(): void {
    this.keycloak
      .login()
      .then((result: any) => {
        this.keycloak
          .getToken()
          .then((data: any) => {
            console.log(data);
          })
          .catch((err: unknown) => {
            console.error('Erreur lors de la récupération du token :', err);
          });
      })
      .catch((err: unknown) => {
        console.error('Erreur lors du login :', err);
      });
  }

  logout(): void {
    from(this.keycloak.logout()).subscribe({
      complete: () => this.accountService.authenticate(null),
    });
  }
}
