import { KeycloakService } from 'keycloak-angular';
import { switchMap } from 'rxjs/operators';
import { ConfigInitService } from './config-init.service';
import { firstValueFrom } from 'rxjs';
import { from } from 'rxjs'; // Ajout de l'importation de from()

export function initializeKeycloak(keycloak: KeycloakService, configService: ConfigInitService) {
  return () =>
    configService.getConfig().pipe(
      switchMap<any, any>(config => {
        return firstValueFrom(
          from(
            // Conversion de la Promise en Observable
            keycloak.init({
              config: {
                url: config['KEYCLOAK_URL'],
                realm: config['KEYCLOAK_REALM'],
                clientId: config['KEYCLOAK_CLIENT_ID'],
              },
              initOptions: {
                onLoad: 'check-sso',
                silentCheckSsoRedirectUri: window.location.origin + '/content/silent-check-sso.html',
                checkLoginIframe: false,
              },
              enableBearerInterceptor: true,
              bearerPrefix: 'Bearer',
              bearerExcludedUrls: ['/content', '/clients/public'],
            }),
          ),
        );
      }),
    );
}
