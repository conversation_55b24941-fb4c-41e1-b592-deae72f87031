{"legal-referentialApp": {"legalReferentialSiren": {"home": {"title": "<PERSON><PERSON>", "refreshListLabel": "Refresh list", "createLabel": "Create a new <PERSON><PERSON>", "createOrEditLabel": "Create or edit a Siren", "notFound": "No Sirens found"}, "created": "A new Siren is created with identifier {{ param }}", "updated": "A Siren is updated with identifier {{ param }}", "deleted": "A Siren is deleted with identifier {{ param }}", "delete": {"question": "Are you sure you want to delete Siren {{ id }}?"}, "detail": {"title": "<PERSON><PERSON>"}, "id": "ID", "siren": "<PERSON><PERSON>", "companyName": "Company Name", "entityType": "Entity Type", "status": "Status", "pkSiret": "Pk Siret", "pkAddressLine": "Pk Address Line"}}}