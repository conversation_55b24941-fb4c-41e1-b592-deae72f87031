{"legalReferentialFrontApp": {"platform": {"home": {"title": "Platforms", "refreshListLabel": "Refresh list", "createLabel": "Create a new Platform", "createOrEditLabel": "Create or edit a Platform", "notFound": "No Platforms found"}, "created": "A new Platform is created with identifier {{ param }}", "updated": "A Platform is updated with identifier {{ param }}", "deleted": "A Platform is deleted with identifier {{ param }}", "delete": {"question": "Are you sure you want to delete Platform {{ id }}?"}, "detail": {"title": "Platform"}, "instanceID": "instanceID", "platformID": "Platform ID", "platformType": "Platform Type", "platformStatus": "Platform Status", "platformMatricule": "Platform Matricule", "platformSiren": "Platform Siren", "platformCompanyName": "Platform Company Name", "platformCommercialName": "Platform Commercial Name", "platformContactOrUrl": "Platform Contact Or Url", "registrationBeginDate": "Registration Begin Date", "registrationEndDate": "Registration End Date", "pkSiren": "Pk Siren"}}}