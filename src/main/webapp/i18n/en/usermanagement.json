{"legalReferentialFrontApp": {"usermanagement": {"home": {"title": "Usermanagements", "refreshListLabel": "Refresh list", "createLabel": "Create a new Usermanagement", "createOrEditLabel": "Create or edit a Usermanagement", "notFound": "No Usermanagements found"}, "created": "A new Usermanagement is created with identifier {{ param }}", "updated": "A Usermanagement is updated with identifier {{ param }}", "deleted": "A Usermanagement is deleted with identifier {{ param }}", "delete": {"question": "Are you sure you want to delete Usermanagement {{ id }}?"}, "detail": {"title": "Usermanagement"}, "id": "ID", "login": "<PERSON><PERSON>", "username": "Email", "firstName": "First name", "lastName": "Last name", "email": "Email", "enabled": "Enabled", "roles": "Roles", "password": "Password"}}}