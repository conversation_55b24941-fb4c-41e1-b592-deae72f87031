{"legalReferentialFrontApp": {"routingCode": {"home": {"title": "Routing Codes", "refreshListLabel": "Refresh list", "createLabel": "Create a new Routing Code", "createOrEditLabel": "Create or edit a Routing Code", "notFound": "No Routing Codes found"}, "created": "A new Routing Code is created with identifier {{ param }}", "updated": "A Routing Code is updated with identifier {{ param }}", "deleted": "A Routing Code is deleted with identifier {{ param }}", "delete": {"question": "Are you sure you want to delete Routing Code {{ id }}?"}, "detail": {"title": "Routing Code"}, "id": "ID", "instanceID": "Instance ID", "routingCodeID": "Routing Code ID", "status": "Status", "routingCodeLabel": "Routing Code Label", "routingCodeType": "Routing Code Type", "administrativeStatus": "Administrative Status", "legalEngagementManagement": "Legal Engagement Management", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "addressLine3": "Address Line 3", "addressPostalCode": "Address Postal Code", "addressCity": "Address City", "addressCountrySubdivision": "Address Country Subdivision", "addressCountryCode": "Address Country Code", "addressCountryLabel": "Address Country Label", "fkRoutingCodeAddressLine": "Fk Routing Code Address Line", "fkRoutingCodeSiret": "Fk Routing Code Siret"}}}