{"legalReferentialFrontApp": {"siret": {"home": {"title": "Sirets", "refreshListLabel": "Actualiser la liste", "createLabel": "<PERSON><PERSON>er un nouveau Siret", "createOrEditLabel": "<PERSON><PERSON><PERSON> ou éditer un Siret", "notFound": "<PERSON><PERSON><PERSON> trouvé"}, "created": "Un nouveau Siret a été créé avec l'identifiant {{ param }}", "updated": "Le Siret avec l'identifiant {{ param }} a été mis à jour", "deleted": "Le Siret avec l'identifiant {{ param }} a été supprimé", "delete": {"question": "Êtes-vous certain de vouloir supprimer le Siret {{ instanceID }} ?"}, "detail": {"title": "<PERSON><PERSON>"}, "id": "ID", "instanceID": "Instance ID", "siret": "<PERSON><PERSON>", "status": "Status", "establishmentType": "Establishment Type", "denomination": "Denomination", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "addressLine3": "Address Line 3", "addressCity": "Address City", "addressCountrySubdivision": "Address Country Subdivision", "addressCountryCode": "Address Country Code", "addressCountryLabel": "Address Country Label", "b2gMoa": "B 2 G Moa", "b2gMoaOnly": "B 2 G Moa Only", "b2gPaymentStatusManagement": "B 2 G Payment Status Management", "b2gLegalEngagementManagement": "B 2 G Legal Engagement Management", "b2gLegalOrServiceEngagementManagement": "B 2 G Legal Or Service Engagement Management", "b2gServiceCodeManagement": "B 2 G Service Code Management", "diffusible": "Diffusible", "addressPostalCode": "Address Postal Code", "pkRoutingCode": "Pk Routing Code", "pkAddressLine": "Pk Address Line", "fkSiretSiren": "Fk Siret Siren"}}}