{"legalReferentialFrontApp": {"routingCode": {"home": {"title": "RoutingCodes", "refreshListLabel": "Actualiser la liste", "createLabel": "Créer un nouveau Routing Code", "createOrEditLabel": "<PERSON><PERSON><PERSON> ou éditer un Routing Code", "notFound": "Aucun Routing Code trouvé"}, "created": "Un nouveau Routing Code a été créé avec l'identifiant {{ param }}", "updated": "Le Routing Code avec l'identifiant {{ param }} a été mis à jour", "deleted": "Le Routing Code avec l'identifiant {{ param }} a été supprimé", "delete": {"question": "Êtes-vous certain de vouloir supprimer le Routing Code {{ instanceID }} ?"}, "detail": {"title": "Routing Code"}, "id": "ID", "instanceID": "Instance ID", "routingCodeID": "Routing Code ID", "status": "Status", "routingCodeLabel": "Routing Code Label", "routingCodeType": "Routing Code Type", "administrativeStatus": "Administrative Status", "legalEngagementManagement": "Legal Engagement Management", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "addressLine3": "Address Line 3", "addressPostalCode": "Address Postal Code", "addressCity": "Address City", "addressCountrySubdivision": "Address Country Subdivision", "addressCountryCode": "Address Country Code", "addressCountryLabel": "Address Country Label", "fkRoutingCodeAddressLine": "Fk Routing Code Address Line", "fkRoutingCodeSiret": "Fk Routing Code Siret"}}}