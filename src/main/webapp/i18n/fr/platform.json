{"legalReferentialFrontApp": {"platform": {"home": {"title": "Platforms", "refreshListLabel": "Actualiser la liste", "createLabel": "Créer un nouveau Platform", "createOrEditLabel": "<PERSON><PERSON><PERSON> ou éditer un Platform", "notFound": "Aucun Platform trouvé"}, "created": "Un nouveau Platform a été créé avec l'identifiant {{ param }}", "updated": "Le Platform avec l'identifiant {{ param }} a été mis à jour", "deleted": "Le Platform avec l'identifiant {{ param }} a été supprimé", "delete": {"question": "Êtes-vous certain de vouloir supprimer le Platform {{ id }} ?"}, "detail": {"title": "Platform"}, "instanceID": "instanceID", "platformID": "Platform ID", "platformType": "Platform Type", "platformStatus": "Platform Status", "platformMatricule": "Platform Matricule", "platformSiren": "Platform Siren", "platformCompanyName": "Platform Company Name", "platformCommercialName": "Platform Commercial Name", "platformContactOrUrl": "Platform Contact Or Url", "registrationBeginDate": "Registration Begin Date", "registrationEndDate": "Registration End Date", "pkSiren": "Pk Siren"}}}