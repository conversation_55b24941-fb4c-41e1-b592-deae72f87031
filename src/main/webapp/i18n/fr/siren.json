{"legalReferentialFrontApp": {"siren": {"home": {"title": "<PERSON><PERSON>", "refreshListLabel": "Actualiser la liste", "createLabel": "<PERSON><PERSON>er un nouveau Siren", "createOrEditLabel": "<PERSON><PERSON><PERSON> ou éditer un Siren", "notFound": "<PERSON><PERSON><PERSON> trouvé"}, "created": "Un nouveau Siren a été créé avec l'identifiant {{ param }}", "updated": "Le Siren avec l'identifiant {{ param }} a été mis à jour", "deleted": "Le Siren avec l'identifiant {{ param }} a été supprimé", "delete": {"question": "Êtes-vous certain de vouloir supprimer le Siren {{ instanceID }} ?"}, "detail": {"title": "<PERSON><PERSON>"}, "id": "ID", "instanceID": "Instance ID", "siren": "<PERSON><PERSON>", "companyName": "Company Name", "entityType": "Entity Type", "status": "Status", "pkSiret": "Pk Siret", "pkAddressLine": "Pk Address Line"}}}