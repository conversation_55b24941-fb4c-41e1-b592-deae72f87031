const AlwaysEnableTryItOutPlugin1 = function (system) {
  return {
    components: {
      TryItOutButton: () => null,
    },
  };
};

window.onload = async function () {
  function getCSRF() {
    var name = 'XSRF-TOKEN=';
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
      var c = ca[i];
      while (c.charAt(0) === ' ') {
        c = c.substring(1);
      }
      if (c.indexOf(name) !== -1) {
        return c.substring(name.length, c.length);
      }
    }
    return '';
  }

  const token = sessionStorage.getItem('token');
  const backURLEndPoint = sessionStorage.getItem('backurl');
  const axiosConfig = {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    timeout: 5000,
  };

  const baseUrl = backURLEndPoint + 'v3/api-docs';
  let urls;

  if (!urls || urls.length === 0) {
    const response = await axios.get(backURLEndPoint + 'management/jhiopenapigroups', axiosConfig);
    if (Array.isArray(response.data)) {
      urls = response.data.map(({ group, description }) => ({ name: description, url: `${baseUrl}/${group}` }));
    } else {
      urls = [{ name: 'default', url: baseUrl }];
    }
  }

  if (urls) {
    urls.sort(function (a, b) {
      var x = a.name.toLowerCase(),
        y = b.name.toLowerCase();
      if (x.includes('(default)')) {
        return -1;
      }
      if (y.includes('(default)')) {
        return 1;
      }
      if (x.includes('(management)')) {
        return -1;
      }
      if (y.includes('(management)')) {
        return 1;
      }
      return x < y ? -1 : x > y ? 1 : 0;
    });
  }

  // Build a system
  var ui = SwaggerUIBundle({
    urls: urls,
    url: baseUrl,
    dom_id: '#swagger-ui',
    deepLinking: true,
    filter: true,
    layout: 'StandaloneLayout',
    withCredentials: true,
    presets: [SwaggerUIBundle.presets.apis, SwaggerUIStandalonePreset],
    plugins: [SwaggerUIBundle.plugins.DownloadUrl, AlwaysEnableTryItOutPlugin1],
    tryItOutEnabled: true,
    requestInterceptor: function (req) {
      req.headers['Authorization'] = 'Bearer ' + token;
      req.headers['Access-Control-Allow-Origin'] = '*';
      // Remove the sample Swagger UI request body if present
      if (req.method === 'GET' && req.body === '{"additionalProp1":"string","additionalProp2":"string","additionalProp3":"string"}') {
        req.body = undefined;
      }
      return req;
    },
  });

  window.ui = ui;
};
