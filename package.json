{"name": "legal-referential-front", "version": "3.0.0-SNAPSHOT", "private": true, "description": "Description for Legal Referential Front", "license": "UNLICENSED", "scripts": {"build": "npm run webapp:prod --", "build-watch": "concurrently 'npm run webapp:build:dev -- --watch'", "ci:frontend:build": "npm run webapp:build:$npm_package_config_default_environment", "ci:frontend:test": "npm run ci:frontend:build && npm test", "clean-www": "rimraf target/classes/static/", "cleanup": "rimraf target/", "jest": "jest --coverage --logHeapUsage --maxWorkers=2 --config jest.conf.js", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "prepare": "husky", "prettier:check": "prettier --check \"{,src/**/,webpack/,.blueprint/**/}*.{md,json,yml,js,cjs,mjs,ts,cts,mts,html,css,scss}\"", "prettier:format": "prettier --write \"{,src/**/,webpack/,.blueprint/**/}*.{md,json,yml,js,cjs,mjs,ts,cts,mts,html,css,scss}\"", "serve": "npm run start --", "sonar": "sonar-scanner", "start": "ng serve --hmr", "start-tls": "npm run webapp:dev-ssl", "pretest": "npm run lint", "test": "ng test --coverage --log-heap-usage -w=2", "test:watch": "npm run test -- --watch", "watch": "concurrently npm:start", "webapp:build": "npm run clean-www && npm run webapp:build:dev", "webapp:build:dev": "ng build --configuration development", "webapp:build:prod": "ng build --configuration production", "webapp:dev": "ng serve", "webapp:dev-ssl": "ng serve --ssl", "webapp:dev-verbose": "ng serve --verbose", "webapp:prod": "npm run clean-www && npm run webapp:build:prod", "webapp:test": "npm run test --", "pretty": "prettier --write src"}, "config": {"default_environment": "prod"}, "dependencies": {"@angular/common": "18.2.9", "@angular/compiler": "18.2.9", "@angular/core": "18.2.9", "@angular/forms": "18.2.9", "@angular/localize": "18.2.9", "@angular/platform-browser": "18.2.9", "@angular/platform-browser-dynamic": "18.2.9", "@angular/router": "18.2.9", "@fortawesome/angular-fontawesome": "0.15.0", "@fortawesome/fontawesome-svg-core": "6.6.0", "@fortawesome/free-solid-svg-icons": "6.6.0", "@ng-bootstrap/ng-bootstrap": "17.0.1", "@ngx-translate/core": "15.0.0", "@ngx-translate/http-loader": "8.0.0", "@popperjs/core": "2.11.8", "bootstrap": "5.3.3", "dayjs": "1.11.13", "keycloak-angular": "^16.0.1", "keycloak-js": "^25.0.6", "ngx-infinite-scroll": "18.0.0", "rxjs": "7.8.1", "tslib": "2.8.0", "zone.js": "0.14.10"}, "devDependencies": {"@angular-builders/custom-webpack": "18.0.0", "@angular-builders/jest": "18.0.0", "@angular-devkit/build-angular": "18.2.10", "@angular/cli": "18.2.10", "@angular/compiler-cli": "18.2.9", "@angular/service-worker": "18.2.9", "@types/jest": "29.5.14", "@types/node": "20.11.25", "angular-eslint": "18.4.0", "browser-sync": "3.0.3", "browser-sync-webpack-plugin": "2.3.0", "buffer": "6.0.3", "concurrently": "9.0.1", "copy-webpack-plugin": "12.0.2", "eslint": "9.13.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-webpack-plugin": "4.2.0", "folder-hash": "4.0.4", "generator-jhipster": "8.7.3", "globals": "15.11.0", "husky": "9.1.6", "jest": "29.7.0", "jest-date-mock": "1.0.10", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "jest-preset-angular": "14.2.4", "jest-sonar": "0.2.16", "lint-staged": "15.2.10", "merge-jsons-webpack-plugin": "2.0.1", "prettier": "3.3.3", "prettier-plugin-packagejson": "2.5.3", "rimraf": "5.0.8", "sonar-scanner": "3.1.0", "swagger-ui-dist": "5.17.14", "ts-jest": "29.2.5", "typescript": "5.5.4", "typescript-eslint": "8.12.2", "wait-on": "8.0.1", "webpack-bundle-analyzer": "4.10.2", "webpack-merge": "6.0.1", "webpack-notifier": "1.15.0"}, "engines": {"node": ">=22.11.0"}, "cacheDirectories": ["node_modules"], "overrides": {"browser-sync": "3.0.3", "webpack": "5.95.0"}}